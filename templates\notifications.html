<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">الإشعارات</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> الرئيسية
            </a>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">قائمة الإشعارات</h5>
                <form action="{{ url_for('api_mark_all_notifications_read') }}" method="post">
                    <button type="submit" class="btn btn-sm btn-light">تعليم الكل كمقروء</button>
                </form>
            </div>
            <div class="card-body">
                {% if notifications %}
                <div class="list-group">
                    {% for notification in notifications %}
                    <div class="list-group-item list-group-item-action {% if not notification.is_read %}bg-light{% endif %}">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">
                                {% if notification.type == 'success' %}
                                <i class="bi bi-check-circle-fill text-success"></i>
                                {% elif notification.type == 'warning' %}
                                <i class="bi bi-exclamation-triangle-fill text-warning"></i>
                                {% elif notification.type == 'danger' %}
                                <i class="bi bi-x-circle-fill text-danger"></i>
                                {% else %}
                                <i class="bi bi-info-circle-fill text-info"></i>
                                {% endif %}
                                {{ notification.title }}
                            </h5>
                            <small>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                        <p class="mb-1">{{ notification.message }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                {% if notification.is_read %}
                                <i class="bi bi-check-all"></i> مقروء
                                {% else %}
                                <i class="bi bi-check"></i> غير مقروء
                                {% endif %}
                            </small>
                            <form action="{{ url_for('api_mark_notification_read', notification_id=notification.id) }}" method="post">
                                <button type="submit" class="btn btn-sm btn-outline-primary {% if notification.is_read %}d-none{% endif %}">
                                    تعليم كمقروء
                                </button>
                            </form>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد إشعارات حال<|im_start|>.
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>