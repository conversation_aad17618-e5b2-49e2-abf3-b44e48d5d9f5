document.addEventListener('DOMContentLoaded', function() {
    // عرض بيانات العميل
    document.querySelectorAll('.view-customer').forEach(button => {
        button.addEventListener('click', function() {
            const customerId = this.getAttribute('data-customer-id');
            
            // جلب بيانات العميل من الخادم
            fetch(`/api/customer/${customerId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('customerName').textContent = data.customer.name;
                        document.getElementById('customerPhone').textContent = data.customer.phone || 'غير متوفر';
                        document.getElementById('customerEmail').textContent = data.customer.email || 'غير متوفر';
                        document.getElementById('customerAddress').textContent = data.customer.address || 'غير متوفر';
                        document.getElementById('customerNotes').textContent = data.customer.notes || 'لا توجد ملاحظات';
                        
                        const balance = data.customer.balance;
                        const balanceElement = document.getElementById('customerBalance');
                        balanceElement.textContent = balance;
                        
                        if (balance > 0) {
                            balanceElement.className = 'text-danger fw-bold';
                        } else {
                            balanceElement.className = 'text-success fw-bold';
                        }
                        
                        new bootstrap.Modal(document.getElementById('viewCustomerModal')).show();
                    }
                })
                .catch(error => console.error('Error:', error));
        });
    });
    
    // تعديل بيانات العميل
    document.querySelectorAll('.edit-customer').forEach(button => {
        button.addEventListener('click', function() {
            const customerId = this.getAttribute('data-customer-id');
            
            // جلب بيانات العميل من الخادم
            fetch(`/api/customer/${customerId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('edit_customer_id').value = data.customer.id;
                        document.getElementById('edit_name').value = data.customer.name;
                        document.getElementById('edit_phone').value = data.customer.phone || '';
                        document.getElementById('edit_email').value = data.customer.email || '';
                        document.getElementById('edit_address').value = data.customer.address || '';
                        document.getElementById('edit_notes').value = data.customer.notes || '';
                        
                        new bootstrap.Modal(document.getElementById('editCustomerModal')).show();
                    }
                })
                .catch(error => console.error('Error:', error));
        });
    });
    
    // البحث في قائمة العملاء
    document.getElementById('customerSearch').addEventListener('keyup', function() {
        const searchText = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            const name = row.cells[0].textContent.toLowerCase();
            const phone = row.cells[1].textContent.toLowerCase();
            const email = row.cells[2].textContent.toLowerCase();
            
            if (name.includes(searchText) || phone.includes(searchText) || email.includes(searchText)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});