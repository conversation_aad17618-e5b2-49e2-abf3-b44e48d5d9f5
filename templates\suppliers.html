<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">إدارة الموردين</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> الرئيسية
            </a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">إضافة مورد جديد</h5>
                    </div>
                    <div class="card-body">
                        <form id="supplierForm" action="{{ url_for('add_supplier') }}" method="post">
                            <input type="hidden" name="supplier_id" id="supplier_id">
                            
                            <div class="mb-3">
                                <label class="form-label">اسم المورد:</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف:</label>
                                <input type="text" class="form-control" name="phone">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني:</label>
                                <input type="email" class="form-control" name="email">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">العنوان:</label>
                                <input type="text" class="form-control" name="address">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">ملاحظات:</label>
                                <textarea class="form-control" name="notes" rows="3"></textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">حفظ</button>
                                <button type="button" id="resetForm" class="btn btn-secondary mt-2">إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">قائمة الموردين</h5>
                        <button type="button" class="btn btn-light btn-sm" id="printSuppliers">
                            <i class="bi bi-printer"></i> طباعة
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="searchInput" placeholder="بحث عن مورد...">
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>رقم الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>العنوان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for supplier in suppliers %}
                                    <tr>
                                        <td>{{ supplier.name }}</td>
                                        <td>{{ supplier.phone }}</td>
                                        <td>{{ supplier.email }}</td>
                                        <td>{{ supplier.address }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-info edit-supplier" 
                                                    data-id="{{ supplier.id }}"
                                                    data-name="{{ supplier.name }}"
                                                    data-phone="{{ supplier.phone }}"
                                                    data-email="{{ supplier.email }}"
                                                    data-address="{{ supplier.address }}"
                                                    data-notes="{{ supplier.notes }}">
                                                <i class="bi bi-pencil"></i> تعديل
                                            </button>
                                            <a href="{{ url_for('delete_supplier', id=supplier.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المورد؟')">
                                                <i class="bi bi-trash"></i> حذف
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تعديل المورد
            document.querySelectorAll('.edit-supplier').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    const phone = this.getAttribute('data-phone');
                    const email = this.getAttribute('data-email');
                    const address = this.getAttribute('data-address');
                    const notes = this.getAttribute('data-notes');
                    
                    document.getElementById('supplier_id').value = id;
                    document.querySelector('input[name="name"]').value = name;
                    document.querySelector('input[name="phone"]').value = phone || '';
                    document.querySelector('input[name="email"]').value = email || '';
                    document.querySelector('input[name="address"]').value = address || '';
                    document.querySelector('textarea[name="notes"]').value = notes || '';
                    
                    document.getElementById('supplierForm').action = "{{ url_for('update_supplier') }}";
                    document.querySelector('button[type="submit"]').textContent = 'تحديث';
                });
            });
            
            // إعادة تعيين النموذج
            document.getElementById('resetForm').addEventListener('click', function() {
                document.getElementById('supplierForm').reset();
                document.getElementById('supplier_id').value = '';
                document.getElementById('supplierForm').action = "{{ url_for('add_supplier') }}";
                document.querySelector('button[type="submit"]').textContent = 'حفظ';
            });
            
            // البحث في الموردين
            document.getElementById('searchInput').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const tableRows = document.querySelectorAll('tbody tr');
                
                tableRows.forEach(row => {
                    const name = row.cells[0].textContent.toLowerCase();
                    const phone = row.cells[1].textContent.toLowerCase();
                    const email = row.cells[2].textContent.toLowerCase();
                    const address = row.cells[3].textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || phone.includes(searchTerm) || 
                        email.includes(searchTerm) || address.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
            
            // طباعة قائمة الموردين
            document.getElementById('printSuppliers').addEventListener('click', function() {
                window.print();
            });
        });
    </script>
</body>
</html>

