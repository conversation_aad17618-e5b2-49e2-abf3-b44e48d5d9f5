<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل المهام - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">سجل المهام</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> الرئيسية
            </a>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">سجل تنفيذ المهام</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المهمة</th>
                                <th>وقت التنفيذ</th>
                                <th>الحالة</th>
                                <th>النتيجة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr>
                                <td>{{ task.name }}</td>
                                <td>{{ task.executed_at }}</td>
                                <td>
                                    {% if task.status == 'success' %}
                                    <span class="badge bg-success">ناجح</span>
                                    {% elif task.status == 'warning' %}
                                    <span class="badge bg-warning text-dark">تحذير</span>
                                    {% elif task.status == 'error' %}
                                    <span class="badge bg-danger">فشل</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ task.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ task.result }}</td>
                                <td>
                                    <a href="{{ url_for('task_status', task_id=task.id) }}" class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>