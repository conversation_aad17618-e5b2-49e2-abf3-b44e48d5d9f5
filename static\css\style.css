/* Estilos generales */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Animaciones para las tarjetas */
.card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Estilos para los iconos */
.bi {
    margin-bottom: 10px;
}

/* Estilos para los botones */
.btn {
    border-radius: 5px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #b02a37;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

/* Estilos para las alertas */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Estilos para las tablas */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Estilos para los formularios */
.form-control, .form-select {
    border-radius: 5px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Estilos para los modales */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
}

.modal-footer {
    border-top: 1px solid #eee;
    padding: 15px 20px;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 6px 15px;
    }
}
