<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة المهمة - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">حالة المهمة</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> الرئيسية
            </a>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">معلومات المهمة</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>معرف المهمة:</strong>
                    <span class="text-muted">{{ task_id }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>حالة المهمة:</strong>
                    {% if task_info.state == 'PENDING' %}
                    <span class="badge bg-info">قيد التنفيذ</span>
                    {% elif task_info.state == 'SUCCESS' %}
                    <span class="badge bg-success">تم الإنجاز</span>
                    {% else %}
                    <span class="badge bg-danger">{{ task_info.state }}</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>نتيجة المهمة:</strong>
                    <div id="taskResult">
                        {% if task_info.state == 'PENDING' %}
                        <div class="alert alert-info">جاري تنفيذ المهمة...</div>
                        {% elif task_info.state == 'SUCCESS' %}
                            {% if task_info.result.status == 'success' %}
                            <div class="alert alert-success">{{ task_info.result.message }}</div>
                            {% elif task_info.result.status == 'warning' %}
                            <div class="alert alert-warning">{{ task_info.result.message }}</div>
                            {% elif task_info.result.status == 'error' %}
                            <div class="alert alert-danger">{{ task_info.result.message }}</div>
                            {% endif %}
                        {% else %}
                        <div class="alert alert-danger">{{ task_info.status }}</div>
                        {% endif %}
                    </div>
                </div>
                
                {% if task_info.state == 'PENDING' %}
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                </div>
                {% endif %}
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('scheduled_tasks') }}" class="btn btn-primary">
                        <i class="bi bi-arrow-left"></i> العودة إلى المهام المجدولة
                    </a>
                    <button class="btn btn-info" id="refreshStatus">
                        <i class="bi bi-arrow-clockwise"></i> تحديث الحالة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const taskId = '{{ task_id }}';
            const refreshButton = document.getElementById('refreshStatus');
            
            // دالة لتحديث حالة المهمة
            function updateTaskStatus() {
                fetch(`/api/task_status/${taskId}`)
                    .then(response => response.json())
                    .then(data => {
                        let resultHtml = '';
                        
                        if (data.state === 'SUCCESS') {
                            if (data.result.status === 'success') {
                                resultHtml = `<div class="alert alert-success">${data.result.message}</div>`;
                            } else if (data.result.status === 'warning') {
                                resultHtml = `<div class="alert alert-warning">${data.result.message}</div>`;
                            } else if (data.result.status === 'error') {
                                resultHtml = `<div class="alert alert-danger">${data.result.message}</div>`;
                            }
                            
                            // إزالة شريط التقدم
                            document.querySelector('.progress')?.remove();
                        } else {
                            resultHtml = `<div class="alert alert-info">${data.status}</div>`;
                        }
                        
                        document.getElementById('taskResult').innerHTML = resultHtml;
                    })
                    .catch(error => console.error('Error:', error));
            }
            
            // تحديث الحالة عند النقر على زر التحديث
            refreshButton.addEventListener('click', updateTaskStatus);
            
            // تحديث الحالة تلقائيًا كل 5 ثوانٍ إذا كانت المهمة قيد التنفيذ
            if ('{{ task_info.state }}' === 'PENDING') {
                const interval = setInterval(function() {
                    updateTaskStatus();
                    
                    // إيقاف التحديث التلقائي إذا اكتملت المهمة
                    if (document.querySelector('.badge').textContent !== 'قيد التنفيذ') {
                        clearInterval(interval);
                    }
                }, 5000);
            }
        });
    </script>
</body>
</html>

