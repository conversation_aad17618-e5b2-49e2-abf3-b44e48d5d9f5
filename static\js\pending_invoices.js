document.addEventListener('DOMContentLoaded', function() {
    // البحث حسب اسم العميل
    document.getElementById('searchByCustomer').addEventListener('click', function() {
        const searchText = document.getElementById('customerSearch').value.toLowerCase();
        filterInvoices(2, searchText); // العمود رقم 2 هو اسم العميل
    });
    
    // البحث حسب رقم الهاتف
    document.getElementById('searchByPhone').addEventListener('click', function() {
        const searchText = document.getElementById('phoneSearch').value;
        // هنا يمكن إضافة منطق البحث عن رقم الهاتف
    });
    
    // البحث حسب التاريخ
    document.getElementById('searchByDate').addEventListener('click', function() {
        const searchDate = document.getElementById('dateSearch').value;
        filterInvoices(1, searchDate); // العمود رقم 1 هو التاريخ
    });
    
    // زر الدفع
    document.querySelectorAll('.pay-btn').forEach(button => {
        button.addEventListener('click', function() {
            const invoiceId = this.getAttribute('data-invoice-id');
            
            // جلب بيانات الفاتورة من الخادم
            fetch(`/api/invoice/${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('payment_invoice_id').value = data.invoice.id;
                        document.getElementById('remaining_amount').value = data.invoice.remaining;
                        document.getElementById('payment_amount').value = data.invoice.remaining;
                    }
                })
                .catch(error => console.error('Error:', error));
        });
    });
    
    // زر الترحيل
    document.querySelectorAll('.process-btn').forEach(button => {
        button.addEventListener('click', function() {
            const invoiceId = this.getAttribute('data-invoice-id');
            
            if (confirm('هل أنت متأكد من ترحيل هذه الفاتورة؟')) {
                window.location.href = `/process_invoice/${invoiceId}`;
            }
        });
    });
    
    // زر الإلغاء
    document.querySelectorAll('.cancel-btn').forEach(button => {
        button.addEventListener('click', function() {
            const invoiceId = this.getAttribute('data-invoice-id');
            document.getElementById('cancel_invoice_id').value = invoiceId;
        });
    });
});

// تصفية الفواتير حسب نص البحث
function filterInvoices(columnIndex, searchText) {
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const cellText = row.cells[columnIndex].textContent.toLowerCase();
        
        if (cellText.includes(searchText.toLowerCase())) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}