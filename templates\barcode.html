<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الباركود - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="row mb-3">
            <div class="col">
                <h2>طباعة الباركود</h2>
            </div>
            <div class="col text-end">
                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-return-right"></i> رجوع
                </a>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">اختيار المنتج</h5>
                    </div>
                    <div class="card-body">
                        <form id="barcodeForm">
                            <div class="mb-3">
                                <label class="form-label">المنتج</label>
                                <select class="form-select" id="productSelect">
                                    <option value="">اختر منتج...</option>
                                    {% for product in products %}
                                    <option value="{{ product.id }}" data-code="{{ product.code }}" data-name="{{ product.name }}" data-price="{{ product.price }}">{{ product.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد النسخ</label>
                                <input type="number" class="form-control" id="copiesInput" min="1" value="1">
                            </div>
                            <button type="button" id="generateBtn" class="btn btn-primary">إنشاء الباركود</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">الباركود</h5>
                    </div>
                    <div class="card-body">
                        <div id="barcodeContainer" class="row"></div>
                        <div class="text-center mt-3">
                            <button id="printBtn" class="btn btn-success d-none">
                                <i class="bi bi-printer"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/barcode.js') }}"></script>
</body>
</html>