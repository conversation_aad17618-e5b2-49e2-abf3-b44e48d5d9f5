<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المصروفات - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">تقرير المصروفات</h1>
            <div>
                <a href="{{ url_for('reports') }}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-left"></i> العودة إلى التقارير
                </a>
                <button class="btn btn-outline-success" id="printReport">
                    <i class="bi bi-printer"></i> طباعة التقرير
                </button>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">اختيار الفترة الزمنية</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-5">
                        <label for="start_date" class="form-label">تاريخ البداية</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-5">
                        <label for="end_date" class="form-label">تاريخ النهاية</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">عرض</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-danger text-white h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">إجمالي المصروفات</h3>
                        <h2 class="display-6">{{ total_expenses|round(2) }}</h2>
                        <p class="mb-0">{{ default_currency }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">متوسط المصروفات اليومية</h3>
                        <h2 class="display-6">{{ daily_average|round(2) }}</h2>
                        <p class="mb-0">{{ default_currency }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-warning text-dark h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">عدد المصروفات</h3>
                        <h2 class="display-6">{{ expenses|length }}</h2>
                        <p class="mb-0">مصروف</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">المصروفات حسب النوع</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="expenseTypeChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">المصروفات اليومية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="dailyExpensesChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">تفاصيل المصروفات</h5>
                <button class="btn btn-light btn-sm" id="exportCSV">
                    <i class="bi bi-file-earmark-excel"></i> تصدير CSV
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>نوع المصروف</th>
                                <th>المبلغ</th>
                                <th>العملة</th>
                                <th>ملاحظات</th>
                                <th>تم بواسطة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for expense in expenses %}
                            <tr>
                                <td>{{ expense.date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ expense.expense_type }}</td>
                                <td>{{ expense.amount|round(2) }}</td>
                                <td>{{ expense.currency }}</td>
                                <td>{{ expense.notes }}</td>
                                <td>{{ expense.created_by_user.username if expense.created_by_user else 'غير معروف' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني للمصروفات حسب النوع
            const expenseTypeCtx = document.getElementById('expenseTypeChart').getContext('2d');
            const expenseTypeChart = new Chart(expenseTypeCtx, {
                type: 'pie',
                data: {
                    labels: [{% for type, amount in expense_types.items() %}'{{ type }}',{% endfor %}],
                    datasets: [{
                        data: [{% for type, amount in expense_types.items() %}{{ amount }},{% endfor %}],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // رسم بياني للمصروفات اليومية
            const dailyExpensesCtx = document.getElementById('dailyExpensesChart').getContext('2d');
            const dailyExpensesChart = new Chart(dailyExpensesCtx, {
                type: 'line',
                data: {
                    labels: {{ expense_dates|tojson }},
                    datasets: [{
                        label: 'المصروفات اليومية',
                        data: {{ expense_values|tojson }},
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // طباعة التقرير
            document.getElementById('printReport').addEventListener('click', function() {
                window.print();
            });
            
            // تصدير CSV
            document.getElementById('exportCSV').addEventListener('click', function() {
                // إنشاء محتوى CSV
                let csvContent = 'التاريخ,نوع المصروف,المبلغ,العملة,ملاحظات,تم بواسطة\n';
                
                {% for expense in expenses %}
                csvContent += '{{ expense.date.strftime("%Y-%m-%d %H:%M") }},"{{ expense.expense_type }}",{{ expense.amount }},"{{ expense.currency }}","{{ expense.notes|replace("\n", " ")|replace("\"", "\"\"") }}","{{ expense.created_by_user.username if expense.created_by_user else "غير معروف" }}"\n';
                {% endfor %}
                
                // إنشاء رابط تنزيل
                const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent);
                const link = document.createElement('a');
                link.setAttribute('href', encodedUri);
                link.setAttribute('download', 'تقرير_المصروفات_{{ start_date }}_{{ end_date }}.csv');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });
        });
    </script>
</body>
</html>


