<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Incluir Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stat-card {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s, transform 0.5s;
        }
        .welcome-title {
            font-size: 1.8rem;
            font-weight: 600;
        }
        .welcome-subtitle {
            font-size: 1.1rem;
            color: #6c757d;
        }
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
        }
        .quick-action-btn {
            border-radius: 10px;
            transition: all 0.3s;
        }
        .quick-action-btn:hover {
            transform: scale(1.05);
        }
        .activity-item {
            border-left: 3px solid #0d6efd;
            padding-left: 15px;
            margin-bottom: 15px;
            position: relative;
        }
        .activity-item::before {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #0d6efd;
            left: -7px;
            top: 5px;
        }
        .activity-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="welcome-title">مرحباً، {{ current_user.username }}</div>
                <div class="welcome-subtitle"></div>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" id="refreshDashboard">
                        <i class="bi bi-arrow-clockwise"></i> تحديث
                    </button>
                    <a href="{{ url_for('scheduled_tasks') }}" class="btn btn-outline-primary">
                        <i class="bi bi-gear"></i> الإعدادات
                    </a>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card dashboard-card stat-card h-100 bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي المبيعات اليوم</h6>
                                <h2 class="mb-0">{{ today_sales }}</h2>
                            </div>
                            <div class="fs-1">
                                <i class="bi bi-cart-check"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small>{{ today_invoices_count }} فاتورة</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card stat-card h-100 bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي المبيعات الشهر</h6>
                                <h2 class="mb-0">{{ month_sales }}</h2>
                            </div>
                            <div class="fs-1">
                                <i class="bi bi-calendar-check"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small>{{ month_invoices_count }} فاتورة</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card stat-card h-100 bg-warning text-dark">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">الفواتير المعلقة</h6>
                                <h2 class="mb-0">{{ pending_invoices_count }}</h2>
                            </div>
                            <div class="fs-1">
                                <i class="bi bi-hourglass-split"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small>{{ pending_amount }} إجمالي المبلغ</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card dashboard-card stat-card h-100 bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">المنتجات منخفضة المخزون</h6>
                                <h2 class="mb-0">{{ low_stock_count }}</h2>
                            </div>
                            <div class="fs-1">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{{ url_for('inventory') }}" class="text-white">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card dashboard-card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">المبيعات الشهرية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salesChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card dashboard-card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">توزيع المبيعات</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salesDistributionChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">أحدث الفواتير</h5>
                        <a href="{{ url_for('invoices') }}" class="btn btn-sm btn-light">عرض الكل</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for invoice in recent_invoices %}
                                    <tr>
                                        <td>{{ invoice.invoice_number }}</td>
                                        <td>{{ invoice.date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ invoice.customer.name if invoice.customer else 'عميل نقدي' }}</td>
                                        <td>{{ invoice.total_amount }}</td>
                                        <td>
                                            <span class="badge {% if invoice.status == 'مكتملة' %}bg-success{% elif invoice.status == 'معلقة' %}bg-warning{% else %}bg-secondary{% endif %}">
                                                {{ invoice.status }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">إجراءات سريعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="{{ url_for('new_invoice') }}" class="btn btn-primary w-100 quick-action-btn">
                                            <i class="bi bi-receipt"></i><br>
                                            فاتورة جديدة
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="{{ url_for('add_product_page') }}" class="btn btn-success w-100 quick-action-btn">
                                            <i class="bi bi-box-seam"></i><br>
                                            منتج جديد
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="{{ url_for('add_customer_page') }}" class="btn btn-info w-100 quick-action-btn">
                                            <i class="bi bi-person-plus"></i><br>
                                            عميل جديد
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="{{ url_for('expenses') }}" class="btn btn-warning w-100 quick-action-btn">
                                            <i class="bi bi-cash-coin"></i><br>
                                            مصروف جديد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">آخر النشاطات</h5>
                            </div>
                            <div class="card-body">
                                <div class="activity-list">
                                    {% for activity in recent_activities %}
                                    <div class="activity-item">
                                        <div class="activity-content">{{ activity.description }}</div>
                                        <div class="activity-time">{{ activity.time.strftime('%Y-%m-%d %H:%M') }}</div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
    <script>
        // بيانات الرسم البياني للمبيعات
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(salesCtx, {
            type: 'bar',
            data: {
                labels: {{ sales_labels|tojson }},
                datasets: [{
                    label: 'المبيعات الشهرية',
                    data: {{ sales_values|tojson }},
                    backgroundColor: 'rgba(13, 110, 253, 0.7)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // بيانات الرسم البياني لتوزيع المبيعات
        const distributionCtx = document.getElementById('salesDistributionChart').getContext('2d');
        const distributionChart = new Chart(distributionCtx, {
            type: 'pie',
            data: {
                labels: {{ distribution_labels|tojson }},
                datasets: [{
                    data: {{ distribution_values|tojson }},
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
        
        // تحديث لوحة التحكم
        document.getElementById('refreshDashboard').addEventListener('click', function() {
            location.reload();
        });
    </script>
</body>
</html>

