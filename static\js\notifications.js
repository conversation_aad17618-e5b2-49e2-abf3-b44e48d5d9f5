document.addEventListener('DOMContentLoaded', function() {
    // تحديث عدد الإشعارات غير المقروءة
    function updateNotificationCount() {
        fetch('/api/notifications/count')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const badge = document.getElementById('notification-badge');
                    if (data.count > 0) {
                        badge.textContent = data.count;
                        badge.classList.remove('d-none');
                    } else {
                        badge.classList.add('d-none');
                    }
                }
            })
            .catch(error => console.error('Error:', error));
    }
    
    // تحميل الإشعارات
    function loadNotifications() {
        fetch('/api/notifications')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const container = document.getElementById('notifications-container');
                    
                    if (data.notifications.length === 0) {
                        container.innerHTML = '<div class="text-center p-3">لا توجد إشعارات</div>';
                        return;
                    }
                    
                    let html = '';
                    data.notifications.forEach(notification => {
                        let iconClass = 'bi-info-circle-fill text-info';
                        if (notification.type === 'success') {
                            iconClass = 'bi-check-circle-fill text-success';
                        } else if (notification.type === 'warning') {
                            iconClass = 'bi-exclamation-triangle-fill text-warning';
                        } else if (notification.type === 'danger') {
                            iconClass = 'bi-x-circle-fill text-danger';
                        }
                        
                        html += `
                            <a href="#" class="dropdown-item d-flex align-items-center py-2 ${notification.is_read ? '' : 'bg-light'}" data-id="${notification.id}">
                                <div class="me-3">
                                    <i class="bi ${iconClass} fs-5"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">${notification.title}</h6>
                                        <small class="text-muted">${notification.time}</small>
                                    </div>
                                    <p class="mb-0 small">${notification.message}</p>
                                </div>
                            </a>
                        `;
                    });
                    
                    container.innerHTML = html;
                    
                    // إضافة مستمعي الأحداث للإشعارات
                    container.querySelectorAll('.dropdown-item').forEach(item => {
                        item.addEventListener('click', function(e) {
                            e.preventDefault();
                            const id = this.dataset.id;
                            markNotificationRead(id);
                        });
                    });
                }
            })
            .catch(error => console.error('Error:', error));
    }
    
    // تعليم إشعار كمقروء
    function markNotificationRead(id) {
        fetch(`/api/notifications/mark_read/${id}`, {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث واجهة المستخدم
                    document.querySelector(`.dropdown-item[data-id="${id}"]`).classList.remove('bg-light');
                    updateNotificationCount();
                }
            })
            .catch(error => console.error('Error:', error));
    }
    
    // تعليم جميع الإشعارات كمقروءة
    function markAllNotificationsRead() {
        fetch('/api/notifications/mark_all_read', {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث واجهة المستخدم
                    document.querySelectorAll('.dropdown-item').forEach(item => {
                        item.classList.remove('bg-light');
                    });
                    updateNotificationCount();
                }
            })
            .catch(error => console.error('Error:', error));
    }
    
    // إضافة مستمع حدث لزر "تعليم الكل كمقروء"
    const markAllReadButton = document.getElementById('mark-all-read');
    if (markAllReadButton) {
        markAllReadButton.addEventListener('click', function(e) {
            e.preventDefault();
            markAllNotificationsRead();
        });
    }
    
    // تحديث عدد الإشعارات وتحميلها عند تحميل الصفحة
    updateNotificationCount();
    loadNotifications();
    
    // تحديث الإشعارات كل دقيقة
    setInterval(function() {
        updateNotificationCount();
        loadNotifications();
    }, 60000);
});

