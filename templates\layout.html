<!-- إضافة زر الإشعارات في شريط التنقل -->
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-bell-fill"></i>
        <span id="notification-badge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger d-none">
            0
        </span>
    </a>
    <div class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown">
        <div class="d-flex justify-content-between align-items-center p-2 border-bottom">
            <h6 class="dropdown-header m-0">الإشعارات</h6>
            <button id="mark-all-read" class="btn btn-sm btn-link text-decoration-none">تعليم الكل كمقروء</button>
        </div>
        <div id="notifications-container" style="max-height: 300px; overflow-y: auto;">
            <div class="text-center p-3">جاري تحميل الإشعارات...</div>
        </div>
        <div class="dropdown-divider"></div>
        <a class="dropdown-item text-center" href="{{ url_for('notifications') }}">عرض جميع الإشعارات</a>
    </div>
</li>

<!-- إضافة رابط لصفحة المهام المجدولة في القائمة الجانبية -->
<li class="nav-item">
    <a class="nav-link" href="{{ url_for('scheduled_tasks') }}">
        <i class="bi bi-calendar-check"></i> المهام المجدولة
    </a>
</li>

<!-- إضافة رابط لصفحة سجل المهام في القائمة الجانبية -->
<li class="nav-item">
    <a class="nav-link" href="{{ url_for('task_history') }}">
        <i class="bi bi-clock-history"></i> سجل المهام
    </a>
</li>

<!-- إضافة ملف JavaScript للإشعارات -->
<script src="{{ url_for('static', filename='js/notifications.js') }}"></script>

