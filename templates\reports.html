<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">التقارير</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> الرئيسية
            </a>
        </div>
        
        <div class="row row-cols-1 row-cols-md-3 g-4">
            <div class="col">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up-arrow text-primary" style="font-size: 4rem;"></i>
                        <h3 class="card-title mt-3">تقرير المبيعات</h3>
                        <p class="card-text">عرض تقارير المبيعات والإيرادات خلال فترة زمنية محددة</p>
                        <a href="{{ url_for('sales_report') }}" class="btn btn-primary">
                            <i class="bi bi-eye"></i> عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-cash-coin text-danger" style="font-size: 4rem;"></i>
                        <h3 class="card-title mt-3">تقرير المصروفات</h3>
                        <p class="card-text">عرض تقارير المصروفات والنفقات خلال فترة زمنية محددة</p>
                        <a href="{{ url_for('expenses_report') }}" class="btn btn-danger">
                            <i class="bi bi-eye"></i> عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-box-seam text-success" style="font-size: 4rem;"></i>
                        <h3 class="card-title mt-3">تقرير المخزون</h3>
                        <p class="card-text">عرض تقارير المخزون والمنتجات المتوفرة والمنخفضة</p>
                        <a href="{{ url_for('inventory_report') }}" class="btn btn-success">
                            <i class="bi bi-eye"></i> عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-people text-info" style="font-size: 4rem;"></i>
                        <h3 class="card-title mt-3">تقرير العملاء</h3>
                        <p class="card-text">عرض تقارير العملاء والمبيعات لكل عميل</p>
                        <a href="{{ url_for('customers_report') }}" class="btn btn-info">
                            <i class="bi bi-eye"></i> عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-check text-warning" style="font-size: 4rem;"></i>
                        <h3 class="card-title mt-3">تقرير الأرباح</h3>
                        <p class="card-text">عرض تقارير الأرباح والخسائر خلال فترة زمنية محددة</p>
                        <a href="{{ url_for('profit_report') }}" class="btn btn-warning">
                            <i class="bi bi-eye"></i> عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-clock-history text-secondary" style="font-size: 4rem;"></i>
                        <h3 class="card-title mt-3">المهام المجدولة</h3>
                        <p class="card-text">إدارة المهام المجدولة وإنشاء تقارير تلقائية</p>
                        <a href="{{ url_for('scheduled_tasks') }}" class="btn btn-secondary">
                            <i class="bi bi-gear"></i> إدارة المهام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

