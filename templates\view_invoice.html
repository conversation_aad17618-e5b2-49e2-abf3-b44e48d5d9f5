<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الفاتورة - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">تفاصيل الفاتورة #{{ invoice.invoice_number }}</h5>
                <div>
                    <a href="{{ url_for('invoices') }}" class="btn btn-sm btn-light me-2">
                        <i class="bi bi-plus-circle"></i> فاتورة جديدة
                    </a>
                    <button class="btn btn-sm btn-light me-2" id="printInvoice">
                        <i class="bi bi-printer"></i> طباعة
                    </button>
                    <a href="{{ url_for('invoices') }}" class="btn btn-sm btn-light">
                        <i class="bi bi-arrow-return-right"></i> رجوع
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h4>بيانات الفاتورة</h4>
                        <p><strong>رقم الفاتورة:</strong> {{ invoice.invoice_number }}</p>
                        <p><strong>التاريخ:</strong> {{ invoice.date.strftime('%Y-%m-%d') }}</p>
                        <p><strong>نوع الفاتورة:</strong> {{ invoice.invoice_type }}</p>
                        <p><strong>طريقة الدفع:</strong> {{ invoice.payment_type }}</p>
                        <p><strong>العملة:</strong> {{ invoice.currency }}</p>
                        <p><strong>الحالة:</strong> <span class="badge {{ 'bg-success' if invoice.status == 'مرحلة' else 'bg-warning' if invoice.status == 'غير مرحلة' else 'bg-danger' }}">{{ invoice.status }}</span></p>
                    </div>
                    <div class="col-md-6">
                        <h4>بيانات العميل</h4>
                        <p><strong>اسم العميل:</strong> {{ invoice.customer.name if invoice.customer else 'غير محدد' }}</p>
                        <p><strong>رقم الهاتف:</strong> {{ invoice.customer.phone if invoice.customer and invoice.customer.phone else 'غير محدد' }}</p>
                        <p><strong>العنوان:</strong> {{ invoice.customer.address if invoice.customer and invoice.customer.address else 'غير محدد' }}</p>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">تفاصيل المنتجات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>رمز المنتج</th>
                                        <th>اسم المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in invoice_items %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ item.product.code }}</td>
                                        <td>{{ item.product.name }}</td>
                                        <td>{{ item.price }} {{ invoice.currency }}</td>
                                        <td>{{ item.quantity }}</td>
                                        <td>{{ item.total }} {{ invoice.currency }}</td>
                                    </tr>
                                    {% endfor %}
                                    {% if invoice_items|length == 0 %}
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد منتجات في هذه الفاتورة</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="5" class="text-end">إجمالي الفاتورة</th>
                                        <td>{{ invoice.total_amount }} {{ invoice.currency }}</td>
                                    </tr>
                                    <tr>
                                        <th colspan="5" class="text-end">المبلغ المدفوع</th>
                                        <td>{{ invoice.paid_amount }} {{ invoice.currency }}</td>
                                    </tr>
                                    <tr>
                                        <th colspan="5" class="text-end">المبلغ المتبقي</th>
                                        <td>{{ invoice.remaining_amount }} {{ invoice.currency }}</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                
                {% if invoice.payments %}
                <h4 class="mt-4">سجل المدفوعات</h4>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in invoice.payments %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ payment.amount }}</td>
                                <td>{{ payment.notes }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // طباعة الفاتورة
            document.getElementById('printInvoice').addEventListener('click', function() {
                window.print();
            });
        });
    </script>
</body>
</html>

