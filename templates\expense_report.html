<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المصروفات - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-4">
        <div class="row mb-3">
            <div class="col">
                <h2>تقرير المصروفات</h2>
            </div>
            <div class="col text-end">
                <a href="{{ url_for('expenses') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-return-right"></i> رجوع
                </a>
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="bi bi-printer"></i> طباعة التقرير
                </button>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">معايير البحث</h5>
            </div>
            <div class="card-body">
                <form method="get" action="{{ url_for('expense_report') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">نوع المصروف</label>
                        <select class="form-select" name="expense_type">
                            <option value="">الكل</option>
                            {% for type in expense_types %}
                            <option value="{{ type }}" {% if selected_type == type %}selected{% endif %}>{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">بحث</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">نتائج البحث</h5>
                <span class="badge bg-light text-dark">إجمالي المصروفات: {{ total_amount }}</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>نوع المصروف</th>
                                <th>المبلغ</th>
                                <th>العملة</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for expense in expenses %}
                            <tr>
                                <td>{{ expense.date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ expense.expense_type }}</td>
                                <td>{{ expense.amount }}</td>
                                <td>{{ expense.currency }}</td>
                                <td>{{ expense.notes }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if not expenses %}
                <div class="text-center py-4">
                    <i class="bi bi-exclamation-circle text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-3 text-muted">لا توجد نتائج للبحث</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>


