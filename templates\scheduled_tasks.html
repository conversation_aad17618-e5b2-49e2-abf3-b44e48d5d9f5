<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المهام المجدولة - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">المهام المجدولة</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> الرئيسية
            </a>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">تقرير المبيعات</h5>
                    </div>
                    <div class="card-body">
                        <p>هذه المهمة تقوم بإنشاء تقرير مبيعات للفترة المحددة وإرساله بالبريد الإلكتروني.</p>
                        <form action="{{ url_for('run_sales_report') }}" method="post">
                            <div class="mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="start_date" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="end_date" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" placeholder="أدخل البريد الإلكتروني لاستلام التقرير">
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-file-earmark-text"></i> إنشاء التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">التحقق من المخزون المنخفض</h5>
                    </div>
                    <div class="card-body">
                        <p>هذه المهمة تقوم بالتحقق من المنتجات ذات المخزون المنخفض وإرسال إشعارات.</p>
                        <div class="d-grid">
                            <a href="{{ url_for('run_check_low_stock') }}" class="btn btn-warning">
                                <i class="bi bi-exclamation-triangle"></i> تشغيل المهمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">النسخ الاحتياطي لقاعدة البيانات</h5>
                    </div>
                    <div class="card-body">
                        <p>هذه المهمة تقوم بعمل نسخة احتياطية من قاعدة البيانات وحفظها في مجلد النسخ الاحتياطية.</p>
                        <div class="d-grid">
                            <a href="{{ url_for('run_backup_database') }}" class="btn btn-success">
                                <i class="bi bi-database-check"></i> تشغيل المهمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">إعدادات الجدولة</h5>
            </div>
            <div class="card-body">
                <p>يمكنك تحديد المهام التي ترغب في جدولتها بشكل تلقائي:</p>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="lowStockCheck" checked>
                    <label class="form-check-label" for="lowStockCheck">
                        التحقق من المخزون المنخفض يوم<|im_start|> (الساعة 9:00 صباحsystems)
                    </label>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="backupCheck" checked>
                    <label class="form-check-label" for="backupCheck">
                        النسخ الاحتياطي أسبوعsystems (يوم الأحد الساعة 12:00 صباحsystems)
                    </label>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="salesReportCheck">
                    <label class="form-check-label" for="salesReportCheck">
                        إنشاء تقرير مبيعات شهري (أول يوم من كل شهر)
                    </label>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">البريد الإلكتروني لاستلام التقارير</label>
                    <input type="email" class="form-control" id="reportEmail" placeholder="أدخل البريد الإلكتروني">
                </div>
                
                <div class="d-grid">
                    <button id="saveSchedule" class="btn btn-primary">
                        <i class="bi bi-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">سجل المهام</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المهمة</th>
                                <th>آخر تنفيذ</th>
                                <th>الحالة</th>
                                <th>النتيجة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>التحقق من المخزون المنخفض</td>
                                <td>2023-09-15 09:00:00</td>
                                <td><span class="badge bg-success">ناجح</span></td>
                                <td>تم العثور على 5 منتجات ذات مخزون منخفض</td>
                            </tr>
                            <tr>
                                <td>النسخ الاحتياطي</td>
                                <td>2023-09-10 00:00:00</td>
                                <td><span class="badge bg-success">ناجح</span></td>
                                <td>تم إنشاء النسخة الاحتياطية بنجاح</td>
                            </tr>
                            <tr>
                                <td>تقرير المبيعات</td>
                                <td>2023-09-01 08:00:00</td>
                                <td><span class="badge bg-success">ناجح</span></td>
                                <td>تم إنشاء وإرسال التقرير بنجاح</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // حفظ إعدادات الجدولة
            document.getElementById('saveSchedule').addEventListener('click', function() {
                const lowStockCheck = document.getElementById('lowStockCheck').checked;
                const backupCheck = document.getElementById('backupCheck').checked;
                const salesReportCheck = document.getElementById('salesReportCheck').checked;
                const reportEmail = document.getElementById('reportEmail').value;
                
                // هنا يمكن إضافة كود لحفظ الإعدادات في قاعدة البيانات
                
                alert('تم حفظ إعدادات الجدولة بنجاح');
            });
        });
    </script>
</body>
</html>


