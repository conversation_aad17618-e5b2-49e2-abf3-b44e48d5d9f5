<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المبيعات - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="row mb-3">
            <div class="col">
                <h2>تقرير المبيعات</h2>
            </div>
            <div class="col text-end">
                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-return-right"></i> رجوع
                </a>
                <button class="btn btn-success" onclick="window.print()">
                    <i class="bi bi-printer"></i> طباعة
                </button>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">تصفية التقرير</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="start_date" value="{{ request.args.get('start_date', '') }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="end_date" value="{{ request.args.get('end_date', '') }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العميل</label>
                                <select class="form-select" name="customer_id">
                                    <option value="">الكل</option>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}" {% if request.args.get('customer_id')|int == customer.id %}selected{% endif %}>{{ customer.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">تطبيق</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">إجمالي المبيعات</h5>
                    </div>
                    <div class="card-body text-center">
                        <h1 class="display-4">{{ total_sales }}</h1>
                        <p class="text-muted">{{ currency }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">عدد الفواتير</h5>
                    </div>
                    <div class="card-body text-center">
                        <h1 class="display-4">{{ invoice_count }}</h1>
                        <p class="text-muted">فاتورة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">متوسط قيمة الفاتورة</h5>
                    </div>
                    <div class="card-body text-center">
                        <h1 class="display-4">{{ average_invoice }}</h1>
                        <p class="text-muted">{{ currency }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">المبيعات حسب الشهر</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlySalesChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">المبيعات حسب الفئة</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="categorySalesChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">تفاصيل المبيعات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>نوع الدفع</th>
                                <th>خيارات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ invoice.customer.name if invoice.customer else 'عميل نقدي' }}</td>
                                <td>{{ invoice.total_amount }}</td>
                                <td>{{ invoice.payment_type }}</td>
                                <td>
                                    <a href="{{ url_for('view_invoice', id=invoice.id) }}" class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني للمبيعات الشهرية
            const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
            new Chart(monthlySalesCtx, {
                type: 'bar',
                data: {
                    labels: {{ monthly_labels|tojson }},
                    datasets: [{
                        label: 'المبيعات الشهرية',
                        data: {{ monthly_data|tojson }},
                        backgroundColor: 'rgba(13, 110, 253, 0.7)',
                        borderColor: 'rgba(13, 110, 253, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // رسم بياني للمبيعات حسب الفئة
            const categorySalesCtx = document.getElementById('categorySalesChart').getContext('2d');
            new Chart(categorySalesCtx, {
                type: 'pie',
                data: {
                    labels: {{ category_labels|tojson }},
                    datasets: [{
                        data: {{ category_data|tojson }},
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        });
    </script>
</body>
</html>




