document.addEventListener('DOMContentLoaded', function() {
    // إضافة مخزون
    document.querySelectorAll('.add-stock').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            const productName = this.getAttribute('data-product-name');
            
            document.getElementById('stockProductId').value = productId;
            document.getElementById('stockProductName').value = productName;
            
            new bootstrap.Modal(document.getElementById('addStockModal')).show();
        });
    });

    // تنبيهات المخزون المنخفض
    const lowStockItems = document.querySelectorAll('.low-stock');
    if (lowStockItems.length > 0) {
        const toastContainer = document.getElementById('toast-container');
        lowStockItems.forEach(item => {
            setTimeout(() => {
                const toast = document.createElement('div');
                toast.className = 'toast show';
                toast.innerHTML = `
                    <div class="toast-header bg-warning">
                        <strong class="me-auto">تنبيه مخزون</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        المنتج "${item.getAttribute('data-name')}" منخفض في المخزون!
                    </div>
                `;
                toastContainer.appendChild(toast);
            }, 500);
        });
    }
});