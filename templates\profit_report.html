<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الأرباح - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .profit-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        .profit-card:hover {
            transform: translateY(-5px);
        }
        .profit-value {
            font-size: 1.8rem;
            font-weight: 600;
        }
        .profit-label {
            font-size: 1rem;
            color: #6c757d;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">تقرير الأرباح</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> الرئيسية
            </a>
        </div>
        
        <div class="card mb-4">
            <div class="card-body">
                <form id="dateRangeForm" method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i> عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card profit-card h-100 bg-primary text-white">
                    <div class="card-body text-center">
                        <div class="profit-label">إجمالي المبيعات</div>
                        <div class="profit-value">{{ "{:,.2f}".format(total_revenue) }}</div>
                        <div class="small">{{ sales_count }} فاتورة</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card profit-card h-100 bg-info text-white">
                    <div class="card-body text-center">
                        <div class="profit-label">تكلفة البضاعة المباعة</div>
                        <div class="profit-value">{{ "{:,.2f}".format(total_cost) }}</div>
                        <div class="small">{{ purchase_count }} فاتورة شراء</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card profit-card h-100 bg-warning text-dark">
                    <div class="card-body text-center">
                        <div class="profit-label">إجمالي المصروفات</div>
                        <div class="profit-value">{{ "{:,.2f}".format(total_expenses) }}</div>
                        <div class="small">{{ expense_count }} مصروف</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card profit-card h-100 {% if net_profit > 0 %}bg-success{% else %}bg-danger{% endif %} text-white">
                    <div class="card-body text-center">
                        <div class="profit-label">صافي الربح</div>
                        <div class="profit-value">{{ "{:,.2f}".format(net_profit) }}</div>
                        <div class="small">هامش الربح: {{ "{:.2f}".format(profit_margin) }}%</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">المبيعات اليومية</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dailySalesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">توزيع الأرباح</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="profitPieChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">المصروفات حسب النوع</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="expensesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">تفاصيل المصروفات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>نوع المصروف</th>
                                        <th>المبلغ</th>
                                        <th>النسبة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for category, amount in expense_categories.items() %}
                                    <tr>
                                        <td>{{ category }}</td>
                                        <td>{{ "{:,.2f}".format(amount) }}</td>
                                        <td>{{ "{:.2f}".format(amount / total_expenses * 100 if total_expenses > 0 else 0) }}%</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-primary">
                                        <th>الإجمالي</th>
                                        <th>{{ "{:,.2f}".format(total_expenses) }}</th>
                                        <th>100%</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">ملخص الأرباح</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <tbody>
                                    <tr>
                                        <th>إجمالي المبيعات</th>
                                        <td>{{ "{:,.2f}".format(total_revenue) }}</td>
                                    </tr>
                                    <tr>
                                        <th>تكلفة البضاعة المباعة</th>
                                        <td>{{ "{:,.2f}".format(total_cost) }}</td>
                                    </tr>
                                    <tr class="table-success">
                                        <th>إجمالي الربح</th>
                                        <td>{{ "{:,.2f}".format(gross_profit) }}</td>
                                    </tr>
                                    <tr>
                                        <th>إجمالي المصروفات</th>
                                        <td>{{ "{:,.2f}".format(total_expenses) }}</td>
                                    </tr>
                                    <tr class="{% if net_profit > 0 %}table-success{% else %}table-danger{% endif %}">
                                        <th>صافي الربح</th>
                                        <td>{{ "{:,.2f}".format(net_profit) }}</td>
                                    </tr>
                                    <tr>
                                        <th>هامش الربح</th>
                                        <td>{{ "{:.2f}".format(profit_margin) }}%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button class="btn btn-success" onclick="window.print()">
                                <i class="bi bi-printer"></i> طباعة التقرير
                            </button>
                            <button class="btn btn-primary" id="exportPDF">
                                <i class="bi bi-file-earmark-pdf"></i> تصدير PDF
                            </button>
                            <button class="btn btn-info" id="exportExcel">
                                <i class="bi bi-file-earmark-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني دائري للإيرادات والتكاليف
            const profitPieCtx = document.getElementById('profitPieChart').getContext('2d');
            const profitPieChart = new Chart(profitPieCtx, {
                type: 'pie',
                data: {
                    labels: ['إجمالي الربح', 'تكلفة البضاعة المباعة', 'المصروفات'],
                    datasets: [{
                        data: [{{ gross_profit }}, {{ total_cost }}, {{ total_expenses }}],
                        backgroundColor: [
                            'rgba(40, 167, 69, 0.7)',
                            'rgba(0, 123, 255, 0.7)',
                            'rgba(220, 53, 69, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // رسم بياني للمبيعات اليومية
            const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
            const dailySalesChart = new Chart(dailySalesCtx, {
                type: 'line',
                data: {
                    labels: {{ daily_sales.keys()|list|tojson }},
                    datasets: [{
                        label: 'المبيعات اليومية',
                        data: {{ daily_sales.values()|list|tojson }},
                        backgroundColor: 'rgba(13, 110, 253, 0.2)',
                        borderColor: 'rgba(13, 110, 253, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // رسم بياني للمصروفات حسب النوع
            const expensesCtx = document.getElementById('expensesChart').getContext('2d');
            const expensesChart = new Chart(expensesCtx, {
                type: 'doughnut',
                data: {
                    labels: {{ expense_labels|tojson }},
                    datasets: [{
                        data: {{ expense_values|tojson }},
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // تصدير التقرير إلى PDF
            document.getElementById('exportPDF').addEventListener('click', function() {
                alert('سيتم تنفيذ تصدير PDF قريباً');
                // يمكن استخدام مكتبة مثل jsPDF لتنفيذ هذه الوظيفة
            });
            
            // تصدير التقرير إلى Excel
            document.getElementById('exportExcel').addEventListener('click', function() {
                alert('سيتم تنفيذ تصدير Excel قريباً');
                // يمكن استخدام مكتبة مثل SheetJS لتنفيذ هذه الوظيفة
            });
        });
    </script>
</body>
</html>


