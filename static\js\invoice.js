document.addEventListener('DOMContentLoaded', function() {
    // Función para calcular el total de la línea
    function calculateLineTotal() {
        const price = parseFloat(this.closest('tr').querySelector('.product-price').value) || 0;
        const quantity = parseFloat(this.closest('tr').querySelector('.product-quantity').value) || 0;
        const total = price * quantity;
        this.closest('tr').querySelector('.product-total').value = total.toFixed(2);
        
        // Recalcular el total de la factura
        calculateInvoiceTotal();
    }
    
    // Función para calcular el total de la factura
    function calculateInvoiceTotal() {
        let total = 0;
        document.querySelectorAll('.product-total').forEach(function(input) {
            total += parseFloat(input.value) || 0;
        });
        
        document.getElementById('totalAmount').value = total.toFixed(2);
        
        // Calcular el saldo restante
        const paid = parseFloat(document.getElementById('paidAmount').value) || 0;
        const remaining = total - paid;
        document.getElementById('remainingAmount').value = remaining.toFixed(2);
    }
    
    // Función para buscar información del producto por código
    function fetchProductInfo() {
        const productCode = this.value.trim();
        if (!productCode) return;
        
        const row = this.closest('tr');
        
        // Realizar una solicitud AJAX para obtener la información del producto
        fetch(`/api/product/${productCode}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Llenar los campos con la información del producto
                    row.querySelector('.product-name').value = data.product.name;
                    row.querySelector('.product-price').value = data.product.price;
                    
                    // Recalcular el total de la línea
                    const price = parseFloat(data.product.price) || 0;
                    const quantity = parseFloat(row.querySelector('.product-quantity').value) || 0;
                    row.querySelector('.product-total').value = (price * quantity).toFixed(2);
                    
                    // Recalcular el total de la factura
                    calculateInvoiceTotal();
                } else {
                    // Mostrar un mensaje de error si el producto no se encuentra
                    alert('المنتج غير موجود');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء البحث عن المنتج');
            });
    }

    // Agregar event listeners a los campos de precio y cantidad
    document.querySelectorAll('.product-price, .product-quantity').forEach(function(input) {
        input.addEventListener('input', calculateLineTotal);
    });
    
    // Agregar event listener al campo de monto pagado
    const paidAmountInput = document.getElementById('paidAmount');
    if (paidAmountInput) {
        paidAmountInput.addEventListener('input', calculateInvoiceTotal);
    }
    
    // Agregar event listeners a los campos de código de producto
    document.querySelectorAll('.product-code').forEach(function(input) {
        input.addEventListener('blur', fetchProductInfo);
    });
    
    // Calcular totales iniciales
    calculateInvoiceTotal();

    // Función para agregar una nueva fila de producto
    function addProductRow() {
        const tbody = document.querySelector('#productsTable tbody');
        const newRow = document.createElement('tr');
        
        newRow.innerHTML = `
            <td>
                <input type="text" class="form-control product-code" name="product_code[]">
            </td>
            <td>
                <input type="text" class="form-control product-name" name="product_name[]">
            </td>
            <td>
                <input type="number" class="form-control product-price" name="product_price[]" min="0">
            </td>
            <td>
                <input type="number" class="form-control product-quantity" name="product_quantity[]" min="1" value="1">
            </td>
            <td>
                <input type="number" class="form-control product-total" name="product_total[]" readonly>
            </td>
            <td>
                <button type="button" class="btn btn-danger btn-sm remove-row">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(newRow);
        
        // Agregar event listeners a los nuevos campos
        newRow.querySelector('.product-code').addEventListener('blur', fetchProductInfo);
        newRow.querySelector('.product-price').addEventListener('input', calculateLineTotal);
        newRow.querySelector('.product-quantity').addEventListener('input', calculateLineTotal);
        newRow.querySelector('.remove-row').addEventListener('click', removeProductRow);
    }

    // Función para eliminar una fila de producto
    function removeProductRow() {
        this.closest('tr').remove();
        calculateInvoiceTotal();
    }

    // Agregar event listener al botón de agregar fila
    const addRowButton = document.getElementById('addProductRow');
    if (addRowButton) {
        addRowButton.addEventListener('click', addProductRow);
    }

    // Agregar event listeners a los botones de eliminar fila existentes
    document.querySelectorAll('.remove-row').forEach(function(button) {
        button.addEventListener('click', removeProductRow);
    });
});



