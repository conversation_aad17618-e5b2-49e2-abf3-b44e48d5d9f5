from app import app, db, User, Customer, Product, Invoice, InvoiceItem, Expense, Supplier
from flask import render_template, request, redirect, url_for, flash, jsonify, session
from datetime import datetime, date, timedelta
import random
import string
from tasks import generate_sales_report, check_low_stock, backup_database
from celery.result import AsyncResult

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.password == password:  # في التطبيق الحقيقي يجب استخدام تشفير لكلمة المرور
            session['user_id'] = user.id
            session['username'] = user.username
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.pop('user_id', None)
    session.pop('username', None)
    return redirect(url_for('login'))

# الصفحة الرئيسية
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return render_template('index.html', current_user=session.get('username'), current_date=date.today().strftime('%Y-%m-%d'))

# صفحة الفواتير
@app.route('/invoices')
def invoices():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # الحصول على فواتير اليوم
    today = date.today()
    today_invoices = Invoice.query.filter(
        Invoice.date >= datetime.combine(today, datetime.min.time()),
        Invoice.date <= datetime.combine(today, datetime.max.time())
    ).all()
    
    # الحصول على رقم الفاتورة التالي
    last_invoice = Invoice.query.order_by(Invoice.id.desc()).first()
    next_invoice_number = f"INV-{(last_invoice.id + 1 if last_invoice else 1):05d}"
    
    # الحصول على قائمة العملاء
    customers = Customer.query.all()
    
    return render_template('invoices.html', 
                          today=today.strftime('%Y-%m-%d'), 
                          today_invoices=today_invoices,
                          next_invoice_number=next_invoice_number,
                          customers=customers)

# حفظ فاتورة جديدة
@app.route('/create_invoice', methods=['POST'])
def create_invoice():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # Obtener datos básicos de la factura
    invoice_number = request.form.get('invoice_number')
    date_str = request.form.get('date')
    customer_name = request.form.get('customer_name')
    invoice_type = request.form.get('invoice_type', 'مبيعات')
    payment_type = request.form.get('payment_type', 'نقد')
    currency = request.form.get('currency', 'ريال يمني')
    
    # Obtener datos de pago
    total_amount = float(request.form.get('total_amount') or 0)
    paid_amount = float(request.form.get('paid_amount') or 0)
    
    # Convertir fecha a objeto datetime
    invoice_date = datetime.strptime(date_str, '%Y-%m-%d') if date_str else datetime.now()
    
    # Buscar cliente o crear uno nuevo
    customer = Customer.query.filter_by(name=customer_name).first()
    if not customer and customer_name:
        customer = Customer(name=customer_name)
        db.session.add(customer)
        db.session.commit()
    
    # Crear nueva factura
    new_invoice = Invoice(
        invoice_number=invoice_number,
        date=invoice_date,
        customer_id=customer.id if customer else None,
        invoice_type=invoice_type,
        payment_type=payment_type,
        currency=currency,
        total_amount=total_amount,
        paid_amount=paid_amount,
        created_by=session.get('user_id')
    )
    
    db.session.add(new_invoice)
    db.session.commit()
    
    # Obtener datos de productos
    product_codes = request.form.getlist('product_code[]')
    product_names = request.form.getlist('product_name[]')
    product_prices = request.form.getlist('product_price[]')
    product_quantities = request.form.getlist('product_quantity[]')
    
    # Crear elementos de factura
    for i in range(len(product_codes)):
        if product_codes[i] and product_names[i] and float(product_prices[i] or 0) > 0 and int(product_quantities[i] or 0) > 0:
            # Buscar producto o crear uno nuevo
            product = Product.query.filter_by(code=product_codes[i]).first()
            if not product:
                product = Product(
                    code=product_codes[i],
                    name=product_names[i],
                    price=float(product_prices[i] or 0)
                )
                db.session.add(product)
                db.session.commit()
            
            # Crear elemento de factura
            invoice_item = InvoiceItem(
                invoice_id=new_invoice.id,
                product_id=product.id,
                quantity=int(product_quantities[i] or 0),
                price=float(product_prices[i] or 0)
            )
            db.session.add(invoice_item)
    
    db.session.commit()
    
    flash('تم حفظ الفاتورة بنجاح', 'success')
    return redirect(url_for('view_invoice', id=new_invoice.id))

# عرض فاتورة
@app.route('/view_invoice/<int:id>')
def view_invoice(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    invoice = Invoice.query.get_or_404(id)
    
    # Obtener los elementos de la factura
    invoice_items = InvoiceItem.query.filter_by(invoice_id=id).all()
    
    return render_template('view_invoice.html', invoice=invoice, invoice_items=invoice_items)

# طباعة فاتورة
@app.route('/print_invoice/<int:id>')
def print_invoice(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    invoice = Invoice.query.get_or_404(id)
    
    # Obtener los elementos de la factura
    invoice_items = InvoiceItem.query.filter_by(invoice_id=id).all()
    
    return render_template('print_invoice.html', invoice=invoice, invoice_items=invoice_items)

# واجهة برمجة التطبيقات للحصول على بيانات المنتج
@app.route('/api/product/<code>')
def get_product(code):
    product = Product.query.filter_by(code=code).first()
    
    if product:
        return jsonify({
            'success': True,
            'product': {
                'id': product.id,
                'code': product.code,
                'name': product.name,
                'price': product.price
            }
        })
    else:
        return jsonify({
            'success': False,
            'message': 'المنتج غير موجود'
        })

# إضافة مصروف جديد
@app.route('/add_expense', methods=['POST'])
def add_expense():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    date_str = request.form.get('date')
    expense_type = request.form.get('expense_type')
    amount = float(request.form.get('amount') or 0)
    currency = request.form.get('currency')
    notes = request.form.get('notes')
    
    # تحويل التاريخ إلى كائن datetime
    expense_date = datetime.strptime(date_str, '%Y-%m-%d') if date_str else datetime.now()
    
    # إنشاء مصروف جديد
    new_expense = Expense(
        date=expense_date,
        expense_type=expense_type,
        amount=amount,
        currency=currency,
        notes=notes,
        created_by=session.get('user_id')
    )
    
    db.session.add(new_expense)
    db.session.commit()
    
    flash('تم إضافة المصروف بنجاح', 'success')
    return redirect(url_for('expenses'))

# صفحة المصروفات
@app.route('/expenses')
def expenses():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    today = date.today()
    today_expenses = Expense.query.filter(
        Expense.date >= datetime.combine(today, datetime.min.time()),
        Expense.date <= datetime.combine(today, datetime.max.time())
    ).all()
    
    return render_template('expenses.html', 
                          today=today.strftime('%Y-%m-%d'), 
                          today_expenses=today_expenses)

# صفحة تقرير المصروفات
@app.route('/expense_report', methods=['GET', 'POST'])
def expense_report():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    expense_type = request.args.get('expense_type', '')
    
    # تحويل التواريخ إلى كائنات datetime
    start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
    end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
    end_datetime = datetime.combine(end_datetime.date(), datetime.max.time())
    
    # استعلام المصروفات
    query = Expense.query.filter(Expense.date >= start_datetime, Expense.date <= end_datetime)
    
    if expense_type:
        query = query.filter(Expense.expense_type == expense_type)
    
    expenses_list = query.order_by(Expense.date.desc()).all()
    
    # حساب المجموع
    total_amount = sum(expense.amount for expense in expenses_list)
    
    # الحصول على قائمة أنواع المصروفات الفريدة
    expense_types = db.session.query(Expense.expense_type).distinct().all()
    expense_types = [et[0] for et in expense_types]
    
    return render_template('expense_report.html', 
                          expenses=expenses_list,
                          start_date=start_date,
                          end_date=end_date.split()[0] if ' ' in end_date else end_date,
                          selected_type=expense_type,
                          expense_types=expense_types,
                          total_amount=total_amount)

# صفحة قائمة الأسعار
@app.route('/price_list')
def price_list():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    products = Product.query.order_by(Product.name).all()
    return render_template('price_list.html', products=products)

# إضافة منتج جديد
@app.route('/add_product', methods=['POST'])
def add_product():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    code = request.form.get('code')
    name = request.form.get('name')
    category = request.form.get('category')
    price = float(request.form.get('price') or 0)
    
    # التحقق من عدم وجود منتج بنفس الكود
    existing_product = Product.query.filter_by(code=code).first()
    if existing_product:
        flash('يوجد منتج بنفس الكود بالفعل', 'danger')
        return redirect(url_for('price_list'))
    
    # إنشاء منتج جديد
    new_product = Product(
        code=code,
        name=name,
        category=category,
        price=price
    )
    
    db.session.add(new_product)
    db.session.commit()
    
    flash('تم إضافة المنتج بنجاح', 'success')
    return redirect(url_for('price_list'))

# تعديل منتج
@app.route('/edit_product/<int:id>', methods=['POST'])
def edit_product(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    product = Product.query.get_or_404(id)
    
    code = request.form.get('code')
    name = request.form.get('name')
    category = request.form.get('category')
    price = float(request.form.get('price') or 0)
    
    # التحقق من عدم وجود منتج آخر بنفس الكود
    existing_product = Product.query.filter_by(code=code).first()
    if existing_product and existing_product.id != id:
        flash('يوجد منتج آخر بنفس الكود', 'danger')
        return redirect(url_for('price_list'))
    
    # تحديث بيانات المنتج
    product.code = code
    product.name = name
    product.category = category
    product.price = price
    
    db.session.commit()
    
    flash('تم تحديث المنتج بنجاح', 'success')
    return redirect(url_for('price_list'))

# حذف منتج
@app.route('/delete_product/<int:id>')
def delete_product(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    product = Product.query.get_or_404(id)
    
    # التحقق من عدم استخدام المنتج في فواتير
    invoice_items = InvoiceItem.query.filter_by(product_id=id).first()
    if invoice_items:
        flash('لا يمكن حذف المنتج لأنه مستخدم في فواتير', 'danger')
        return redirect(url_for('price_list'))
    
    db.session.delete(product)
    db.session.commit()
    
    flash('تم حذف المنتج بنجاح', 'success')
    return redirect(url_for('price_list'))

# واجهة برمجة التطبيقات للحصول على بيانات المنتج للتعديل
@app.route('/api/product_details/<int:id>')
def get_product_details(id):
    product = Product.query.get_or_404(id)
    
    return jsonify({
        'success': True,
        'product': {
            'id': product.id,
            'code': product.code,
            'name': product.name,
            'category': product.category,
            'price': product.price
        }
    })

# واجهة برمجة التطبيقات للحصول على بيانات الفاتورة
@app.route('/api/invoice/<int:id>')
def get_invoice(id):
    invoice = Invoice.query.get_or_404(id)
    
    return jsonify({
        'success': True,
        'invoice': {
            'id': invoice.id,
            'invoice_number': invoice.invoice_number,
            'date': invoice.date.strftime('%Y-%m-%d'),
            'customer_name': invoice.customer.name if invoice.customer else '',
            'total_amount': invoice.total_amount,
            'paid_amount': invoice.paid_amount,
            'remaining': invoice.remaining,
            'status': invoice.status
        }
    })

# واجهة برمجة التطبيقات للحصول على بيانات المصروف
@app.route('/api/expense/<int:id>')
def get_expense(id):
    expense = Expense.query.get_or_404(id)
    
    return jsonify({
        'success': True,
        'expense': {
            'id': expense.id,
            'date': expense.date.strftime('%Y-%m-%d'),
            'expense_type': expense.expense_type,
            'amount': expense.amount,
            'currency': expense.currency,
            'notes': expense.notes
        }
    })

# تحديث مصروف
@app.route('/update_expense', methods=['POST'])
def update_expense():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    expense_id = request.form.get('expense_id')
    date_str = request.form.get('date')
    expense_type = request.form.get('expense_type')
    amount = float(request.form.get('amount') or 0)
    currency = request.form.get('currency')
    notes = request.form.get('notes')
    
    expense = Expense.query.get_or_404(expense_id)
    
    # تحويل التاريخ إلى كائن datetime
    expense_date = datetime.strptime(date_str, '%Y-%m-%d') if date_str else datetime.now()
    
    # تحديث بيانات المصروف
    expense.date = expense_date
    expense.expense_type = expense_type
    expense.amount = amount
    expense.currency = currency
    expense.notes = notes
    
    db.session.commit()
    
    flash('تم تحديث المصروف بنجاح', 'success')
    return redirect(url_for('expenses'))

# حذف مصروف
@app.route('/delete_expense/<int:id>')
def delete_expense(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    expense = Expense.query.get_or_404(id)
    
    db.session.delete(expense)
    db.session.commit()
    
    flash('تم حذف المصروف بنجاح', 'success')
    return redirect(url_for('expenses'))

# لوحة التحكم
@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # الحصول على إحصائيات اليوم
    today = date.today()
    today_start = datetime.combine(today, datetime.min.time())
    today_end = datetime.combine(today, datetime.max.time())
    
    # عدد فواتير اليوم
    today_invoices_count = Invoice.query.filter(
        Invoice.date >= today_start,
        Invoice.date <= today_end
    ).count()
    
    # إجمالي مبيعات اليوم
    today_sales_query = db.session.query(db.func.sum(Invoice.total_amount)).filter(
        Invoice.date >= today_start,
        Invoice.date <= today_end,
        Invoice.status != 'ملغية'
    ).scalar()
    today_sales = float(today_sales_query) if today_sales_query is not None else 0
    
    # إجمالي مصروفات اليوم
    today_expenses_query = db.session.query(db.func.sum(Expense.amount)).filter(
        Expense.date >= today_start,
        Expense.date <= today_end
    ).scalar()
    today_expenses = float(today_expenses_query) if today_expenses_query is not None else 0
    
    # عدد العملاء
    customers_count = Customer.query.count()
    
    # إحصائيات للعرض
    stats = {
        'today_invoices_count': today_invoices_count,
        'today_sales': today_sales,
        'today_expenses': today_expenses,
        'customers_count': customers_count
    }
    
    # الحصول على آخر الفواتير
    recent_invoices = Invoice.query.order_by(Invoice.date.desc()).limit(5).all()
    
    # الحصول على آخر المصروفات
    recent_expenses = Expense.query.order_by(Expense.date.desc()).limit(5).all()
    
    # بيانات الرسم البياني للمبيعات الشهرية
    sales_labels = []
    sales_values = []
    
    for i in range(5, -1, -1):
        # حساب الشهر
        month = today.month - i
        year = today.year
        
        while month <= 0:
            month += 12
            year -= 1
        
        # تحديد بداية ونهاية الشهر
        month_start = datetime(year, month, 1)
        if month == 12:
            month_end = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            month_end = datetime(year, month + 1, 1) - timedelta(days=1)
        month_end = datetime.combine(month_end, datetime.max.time())
        
        # الحصول على إجمالي المبيعات للشهر
        month_sales_query = db.session.query(db.func.sum(Invoice.total_amount)).filter(
            Invoice.date >= month_start,
            Invoice.date <= month_end,
            Invoice.status != 'ملغية'
        ).scalar()
        month_sales = float(month_sales_query) if month_sales_query is not None else 0
        
        # إضافة البيانات إلى مصفوفة البيانات
        month_name = month_start.strftime('%B')  # اسم الشهر
        sales_labels.append(month_name)
        sales_values.append(month_sales)
    
    # بيانات الرسم البياني لتوزيع المصروفات
    expenses_labels = []
    expenses_values = []
    
    expense_types = db.session.query(
        Expense.expense_type,
        db.func.sum(Expense.amount).label('total')
    ).group_by(Expense.expense_type).order_by(db.func.sum(Expense.amount).desc()).limit(6).all()
    
    for expense_type in expense_types:
        expenses_labels.append(expense_type.expense_type)
        expenses_values.append(float(expense_type.total))
    
    return render_template('dashboard.html',
                          current_user=session.get('username'),
                          current_date=today.strftime('%Y-%m-%d'),
                          stats=stats,
                          recent_invoices=recent_invoices,
                          recent_expenses=recent_expenses,
                          sales_labels=sales_labels,
                          sales_values=sales_values,
                          expenses_labels=expenses_labels,
                          expenses_values=expenses_values)

# تحديث فاتورة موجودة
@app.route('/update_invoice', methods=['POST'])
def update_invoice():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    invoice_id = request.form.get('invoice_id')
    invoice = Invoice.query.get_or_404(invoice_id)
    
    # تحديث بيانات الفاتورة
    invoice.invoice_number = request.form.get('invoice_number')
    date_str = request.form.get('date')
    invoice.date = datetime.strptime(date_str, '%Y-%m-%d') if date_str else datetime.now()
    
    customer_name = request.form.get('customer_name')
    customer = Customer.query.filter_by(name=customer_name).first()
    if not customer and customer_name:
        customer = Customer(name=customer_name)
        db.session.add(customer)
        db.session.commit()
    
    invoice.customer_id = customer.id if customer else None
    invoice.invoice_type = request.form.get('invoice_type', 'مبيعات')
    invoice.payment_type = request.form.get('payment_type', 'نقد')
    invoice.currency = request.form.get('currency', 'ريال يمني')
    invoice.total_amount = float(request.form.get('total_amount') or 0)
    invoice.paid_amount = float(request.form.get('paid_amount') or 0)
    
    db.session.commit()
    
    flash('تم تحديث الفاتورة بنجاح', 'success')
    return redirect(url_for('view_invoice', id=invoice.id))






# API para obtener detalles del producto por código
@app.route('/api/product/<code>')
def get_product_by_code(code):
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})
    
    product = Product.query.filter_by(code=code).first()
    
    if not product:
        return jsonify({'success': False, 'message': 'المنتج غير موجود'})
    
    return jsonify({
        'success': True,
        'product': {
            'id': product.id,
            'code': product.code,
            'name': product.name,
            'price': product.price,
            'category': product.category
        }
    })

# صفحة إدارة المخزون
@app.route('/inventory')
def inventory():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    products = Product.query.all()
    
    # حساب كمية المخزون لكل منتج
    for product in products:
        # حساب المبيعات
        sales = db.session.query(db.func.sum(InvoiceItem.quantity)).join(Invoice).filter(
            InvoiceItem.product_id == product.id,
            Invoice.invoice_type == 'مبيعات'
        ).scalar() or 0
        
        # حساب المشتريات
        purchases = db.session.query(db.func.sum(InvoiceItem.quantity)).join(Invoice).filter(
            InvoiceItem.product_id == product.id,
            Invoice.invoice_type == 'مشتريات'
        ).scalar() or 0
        
        # حساب المخزون الحالي
        product.current_stock = purchases - sales
    
    return render_template('inventory.html', products=products)

# إضافة مخزون جديد
@app.route('/add_stock', methods=['POST'])
def add_stock():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    product_id = request.form.get('product_id')
    quantity = int(request.form.get('quantity') or 0)
    
    if quantity <= 0:
        flash('يجب إدخال كمية صحيحة', 'danger')
        return redirect(url_for('inventory'))
    
    product = Product.query.get_or_404(product_id)
    
    # إنشاء فاتورة مشتريات جديدة
    invoice_number = 'PUR-' + ''.join(random.choices(string.digits, k=6))
    new_invoice = Invoice(
        invoice_number=invoice_number,
        date=datetime.now(),
        invoice_type='مشتريات',
        payment_type='نقد',
        currency='ريال يمني',
        total_amount=product.price * quantity,
        paid_amount=product.price * quantity,
        created_by=session.get('user_id')
    )
    
    db.session.add(new_invoice)
    db.session.commit()
    
    # إضافة عنصر الفاتورة
    invoice_item = InvoiceItem(
        invoice_id=new_invoice.id,
        product_id=product.id,
        quantity=quantity,
        price=product.price
    )
    
    db.session.add(invoice_item)
    db.session.commit()
    
    flash(f'تم إضافة {quantity} وحدة من {product.name} إلى المخزون', 'success')
    return redirect(url_for('inventory'))

# صفحة الموردين
@app.route('/suppliers')
def suppliers():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    suppliers_list = Supplier.query.all()
    return render_template('suppliers.html', suppliers=suppliers_list)

# إضافة مورد جديد
@app.route('/add_supplier', methods=['POST'])
def add_supplier():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    name = request.form.get('name')
    phone = request.form.get('phone')
    email = request.form.get('email')
    address = request.form.get('address')
    notes = request.form.get('notes')
    
    new_supplier = Supplier(
        name=name,
        phone=phone,
        email=email,
        address=address,
        notes=notes
    )
    
    db.session.add(new_supplier)
    db.session.commit()
    
    flash('تم إضافة المورد بنجاح', 'success')
    return redirect(url_for('suppliers'))

# تعديل مورد
@app.route('/update_supplier', methods=['POST'])
def update_supplier():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    supplier_id = request.form.get('supplier_id')
    supplier = Supplier.query.get_or_404(supplier_id)
    
    supplier.name = request.form.get('name')
    supplier.phone = request.form.get('phone')
    supplier.email = request.form.get('email')
    supplier.address = request.form.get('address')
    supplier.notes = request.form.get('notes')
    
    db.session.commit()
    
    flash('تم تحديث المورد بنجاح', 'success')
    return redirect(url_for('suppliers'))

# حذف مورد
@app.route('/delete_supplier/<int:id>')
def delete_supplier(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    supplier = Supplier.query.get_or_404(id)
    
    # التحقق من عدم وجود فواتير مرتبطة بالمورد
    if supplier.invoices:
        flash('لا يمكن حذف المورد لأنه مرتبط بفواتير', 'danger')
        return redirect(url_for('suppliers'))
    
    db.session.delete(supplier)
    db.session.commit()
    
    flash('تم حذف المورد بنجاح', 'success')
    return redirect(url_for('suppliers'))





# صفحة التقارير
@app.route('/reports')
def reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return render_template('reports.html')

# تقرير المبيعات
@app.route('/sales_report')
def sales_report():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # الحصول على نطاق التاريخ من الاستعلام
    start_date_str = request.args.get('start_date', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date_str = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    
    # تحويل التواريخ إلى كائنات datetime
    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    end_date = datetime.strptime(end_date_str + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
    
    # الحصول على الفواتير في النطاق الزمني المحدد
    invoices = Invoice.query.filter(
        Invoice.date >= start_date,
        Invoice.date <= end_date,
        Invoice.invoice_type == 'مبيعات'
    ).order_by(Invoice.date.desc()).all()
    
    # حساب إجماليات المبيعات
    total_sales = sum(invoice.total_amount for invoice in invoices)
    total_paid = sum(invoice.paid_amount for invoice in invoices)
    total_remaining = total_sales - total_paid
    
    # تحليل المبيعات حسب طريقة الدفع
    payment_types = {}
    for invoice in invoices:
        if invoice.payment_type in payment_types:
            payment_types[invoice.payment_type] += invoice.total_amount
        else:
            payment_types[invoice.payment_type] = invoice.total_amount
    
    # الحصول على المنتجات الأكثر مبيعاً
    top_products = db.session.query(
        Product.name, db.func.sum(InvoiceItem.quantity).label('total_quantity')
    ).join(InvoiceItem).join(Invoice).filter(
        Invoice.date >= start_date,
        Invoice.date <= end_date,
        Invoice.invoice_type == 'مبيعات'
    ).group_by(Product.name).order_by(db.func.sum(InvoiceItem.quantity).desc()).limit(5).all()
    
    return render_template(
        'sales_report.html',
        start_date=start_date_str,
        end_date=end_date_str,
        invoices=invoices,
        total_sales=total_sales,
        total_paid=total_paid,
        total_remaining=total_remaining,
        payment_types=payment_types,
        top_products=top_products
    )

# تقرير المصروفات
@app.route('/expenses_report')
def expenses_report():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # الحصول على نطاق التاريخ من الاستعلام
    start_date_str = request.args.get('start_date', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date_str = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    
    # تحويل التواريخ إلى كائنات datetime
    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    end_date = datetime.strptime(end_date_str + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
    
    # الحصول على المصروفات في النطاق الزمني المحدد
    expenses = Expense.query.filter(
        Expense.date >= start_date,
        Expense.date <= end_date
    ).order_by(Expense.date.desc()).all()
    
    # حساب إجمالي المصروفات
    total_expenses = sum(expense.amount for expense in expenses)
    
    # حساب متوسط المصروفات اليومية
    days_count = (end_date.date() - start_date.date()).days + 1
    daily_average = total_expenses / days_count if days_count > 0 else 0
    
    # تحليل المصروفات حسب النوع
    expense_types = {}
    for expense in expenses:
        if expense.expense_type in expense_types:
            expense_types[expense.expense_type] += expense.amount
        else:
            expense_types[expense.expense_type] = expense.amount
    
    # إعداد بيانات الرسم البياني للمصروفات اليومية
    expense_dates = []
    expense_values = []
    
    current_date = start_date.date()
    while current_date <= end_date.date():
        expense_dates.append(current_date.strftime('%Y-%m-%d'))
        
        # حساب مجموع المصروفات لهذا اليوم
        daily_expenses = sum(
            expense.amount for expense in expenses 
            if expense.date.date() == current_date
        )
        expense_values.append(daily_expenses)
        
        current_date += timedelta(days=1)
    
    return render_template(
        'expenses_report.html',
        start_date=start_date_str,
        end_date=end_date_str,
        expenses=expenses,
        total_expenses=total_expenses,
        daily_average=daily_average,
        expense_types=expense_types,
        expense_dates=expense_dates,
        expense_values=expense_values,
        default_currency='ريال يمني'
    )

# تقرير المخزون
@app.route('/inventory_report')
def inventory_report():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # الحصول على جميع المنتجات
    products = Product.query.all()
    
    # حساب إجماليات المخزون
    total_inventory_value = sum(product.quantity * product.purchase_price for product in products)
    low_stock_count = sum(1 for product in products if product.quantity <= product.min_quantity and product.quantity > 0)
    out_of_stock_count = sum(1 for product in products if product.quantity == 0)
    
    # تحليل المنتجات حسب الفئة
    categories = {}
    for product in products:
        if product.category in categories:
            categories[product.category] += 1
        else:
            categories[product.category] = 1
    
    # الحصول على المنتجات الأعلى قيمة في المخزون
    top_value_products = sorted(
        products, 
        key=lambda p: p.quantity * p.purchase_price, 
        reverse=True
    )[:5]
    
    return render_template(
        'inventory_report.html',
        products=products,
        total_inventory_value=total_inventory_value,
        low_stock_count=low_stock_count,
        out_of_stock_count=out_of_stock_count,
        categories=categories,
        top_value_products=top_value_products,
        default_currency='ريال يمني'
    )

# تقرير العملاء
@app.route('/customers_report')
def customers_report():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # استعلام جميع العملاء
    customers = Customer.query.all()
    
    # تجميع بيانات المبيعات لكل عميل
    customer_data = []
    for customer in customers:
        invoices = Invoice.query.filter_by(customer_id=customer.id, invoice_type='مبيعات').all()
        total_purchases = sum(invoice.total_amount for invoice in invoices)
        total_paid = sum(invoice.paid_amount for invoice in invoices)
        total_remaining = sum(invoice.remaining_amount for invoice in invoices)
        invoice_count = len(invoices)
        
        customer_data.append({
            'customer': customer,
            'total_purchases': total_purchases,
            'total_paid': total_paid,
            'total_remaining': total_remaining,
            'invoice_count': invoice_count
        })
    
    # ترتيب العملاء حسب إجمالي المشتريات
    customer_data = sorted(customer_data, key=lambda x: x['total_purchases'], reverse=True)
    
    return render_template(
        'customers_report.html',
        customer_data=customer_data,
        default_currency='ريال يمني'
    )

# تقرير الأرباح
@app.route('/profit_report')
def profit_report():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # الحصول على معلمات التاريخ من الاستعلام
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    
    # إذا لم يتم تحديد تواريخ، استخدم الشهر الحالي
    if not start_date_str or not end_date_str:
        today = datetime.now()
        start_date = datetime(today.year, today.month, 1)
        if today.month == 12:
            end_date = datetime(today.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(today.year, today.month + 1, 1) - timedelta(days=1)
        end_date = end_date.replace(hour=23, minute=59, second=59)
    else:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        end_date = end_date.replace(hour=23, minute=59, second=59)
    
    # استعلام الفواتير في الفترة المحددة
    sales_invoices = Invoice.query.filter(
        Invoice.date.between(start_date, end_date),
        Invoice.invoice_type == 'مبيعات'
    ).all()
    
    purchase_invoices = Invoice.query.filter(
        Invoice.date.between(start_date, end_date),
        Invoice.invoice_type == 'مشتريات'
    ).all()
    
    # حساب إجماليات المبيعات والمشتريات
    total_sales = sum(invoice.total_amount for invoice in sales_invoices)
    total_purchases = sum(invoice.total_amount for invoice in purchase_invoices)
    
    # حساب تكلفة البضاعة المباعة والربح الإجمالي
    total_cost = 0
    total_revenue = 0
    
    for invoice in sales_invoices:
        for item in invoice.items:
            item_cost = item.product.purchase_price * item.quantity
            item_revenue = item.price * item.quantity
            total_cost += item_cost
            total_revenue += item_revenue
    
    gross_profit = total_revenue - total_cost
    
    # حساب المصروفات
    expenses = Expense.query.filter(
        Expense.date.between(start_date, end_date)
    ).all()
    
    # تجميع المصروفات حسب النوع
    expense_categories = {}
    for expense in expenses:
        if expense.expense_type not in expense_categories:
            expense_categories[expense.expense_type] = 0
        expense_categories[expense.expense_type] += expense.amount
    
    total_expenses = sum(expense.amount for expense in expenses)
    
    # حساب صافي الربح
    net_profit = gross_profit - total_expenses
    
    # حساب هامش الربح
    profit_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
    
    # تحضير بيانات الرسم البياني للمبيعات اليومية
    daily_sales = {}
    current_date = start_date
    while current_date <= end_date:
        daily_sales[current_date.strftime('%Y-%m-%d')] = 0
        current_date += timedelta(days=1)
    
    for invoice in sales_invoices:
        date_str = invoice.date.strftime('%Y-%m-%d')
        if date_str in daily_sales:
            daily_sales[date_str] += invoice.total_amount
    
    # تحضير بيانات الرسم البياني للمصروفات حسب النوع
    expense_labels = list(expense_categories.keys())
    expense_values = list(expense_categories.values())
    
    # تحضير بيانات الرسم البياني لتوزيع الأرباح
    profit_distribution = [
        gross_profit,
        total_cost,
        total_expenses
    ]
    
    return render_template(
        'profit_report.html',
        start_date=start_date.strftime('%Y-%m-%d'),
        end_date=end_date.strftime('%Y-%m-%d'),
        total_sales=total_sales,
        total_purchases=total_purchases,
        total_cost=total_cost,
        total_revenue=total_revenue,
        gross_profit=gross_profit,
        total_expenses=total_expenses,
        expense_categories=expense_categories,
        net_profit=net_profit,
        profit_margin=profit_margin,
        daily_sales=daily_sales,
        expense_labels=expense_labels,
        expense_values=expense_values,
        profit_distribution=profit_distribution,
        sales_count=len(sales_invoices),
        purchase_count=len(purchase_invoices),
        expense_count=len(expenses)
    )

@app.route('/api/profit_data')
def api_profit_data():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    
    # الحصول على معلمات التاريخ من الاستعلام
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    
    # إذا لم يتم تحديد تواريخ، استخدم الشهر الحالي
    if not start_date_str or not end_date_str:
        today = datetime.now()
        start_date = datetime(today.year, today.month, 1)
        if today.month == 12:
            end_date = datetime(today.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(today.year, today.month + 1, 1) - timedelta(days=1)
        end_date = end_date.replace(hour=23, minute=59, second=59)
    else:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        end_date = end_date.replace(hour=23, minute=59, second=59)
    
    # استعلام الفواتير في الفترة المحددة
    sales_invoices = Invoice.query.filter(
        Invoice.date.between(start_date, end_date),
        Invoice.invoice_type == 'مبيعات'
    ).all()
    
    # حساب تكلفة البضاعة المباعة والربح الإجمالي
    total_cost = 0
    total_revenue = 0
    
    for invoice in sales_invoices:
        for item in invoice.items:
            item_cost = item.product.purchase_price * item.quantity
            item_revenue = item.price * item.quantity
            total_cost += item_cost
            total_revenue += item_revenue
    
    gross_profit = total_revenue - total_cost
    
    # حساب المصروفات
    expenses = Expense.query.filter(
        Expense.date.between(start_date, end_date)
    ).all()
    
    total_expenses = sum(expense.amount for expense in expenses)
    
    # حساب صافي الربح
    net_profit = gross_profit - total_expenses
    
    return jsonify({
        'total_revenue': total_revenue,
        'total_cost': total_cost,
        'gross_profit': gross_profit,
        'total_expenses': total_expenses,
        'net_profit': net_profit
    })

# صفحة المهام المجدولة
@app.route('/scheduled_tasks')
def scheduled_tasks():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return render_template('scheduled_tasks.html')

# تشغيل مهمة إنشاء تقرير المبيعات
@app.route('/run_sales_report', methods=['POST'])
def run_sales_report():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    start_date = request.form.get('start_date')
    end_date = request.form.get('end_date')
    email = request.form.get('email')
    
    # تشغيل المهمة في الخلفية
    task = generate_sales_report.delay(start_date, end_date, email)
    
    flash(f'تم بدء مهمة إنشاء تقرير المبيعات. معرف المهمة: {task.id}', 'success')
    return redirect(url_for('task_status', task_id=task.id))

# تشغيل مهمة التحقق من المخزون المنخفض
@app.route('/run_check_low_stock')
def run_check_low_stock():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # تشغيل المهمة في الخلفية
    task = check_low_stock.delay()
    
    flash(f'تم بدء مهمة التحقق من المخزون المنخفض. معرف المهمة: {task.id}', 'success')
    return redirect(url_for('task_status', task_id=task.id))

# تشغيل مهمة النسخ الاحتياطي
@app.route('/run_backup_database')
def run_backup_database():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # تشغيل المهمة في الخلفية
    task = backup_database.delay()
    
    flash(f'تم بدء مهمة النسخ الاحتياطي. معرف المهمة: {task.id}', 'success')
    return redirect(url_for('task_status', task_id=task.id))

# صفحة حالة المهمة
@app.route('/task_status/<task_id>')
def task_status(task_id):
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # الحصول على معلومات المهمة من Celery
    task_result = AsyncResult(task_id)
    task_info = {
        'state': task_result.state,
    }
    
    if task_result.state == 'PENDING':
        task_info['status'] = 'جاري التنفيذ...'
    elif task_result.state == 'SUCCESS':
        task_info['result'] = task_result.result
    else:
        task_info['status'] = str(task_result.info)
    
    return render_template('task_status.html', task_id=task_id, task_info=task_info)

# واجهة API لحالة المهمة
@app.route('/api/task_status/<task_id>')
def api_task_status(task_id):
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})
    
    task_result = AsyncResult(task_id)
    
    return jsonify({
        'success': True,
        'task': {
            'id': task_id,
            'state': task_result.state,
            'result': task_result.result if task_result.state == 'SUCCESS' else None,
            'error': str(task_result.info) if task_result.state == 'FAILURE' else None
        }
    })

# صفحة الباركود
@app.route('/barcode')
def barcode():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    products = Product.query.all()
    return render_template('barcode.html', products=products)

# إشعارات المخزون
@app.route('/api/low_stock_notifications')
def low_stock_notifications():
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مصرح'})
    
    low_stock_threshold = 5  # يمكن جعل هذا إعدادًا قابلًا للتكوين
    
    # الحصول على المنتجات ذات المخزون المنخفض
    products = Product.query.all()
    low_stock_products = []
    
    for product in products:
        # حساب المخزون الحالي
        stock_in = db.session.query(func.sum(InvoiceItem.quantity)).\
            join(Invoice).\
            filter(InvoiceItem.product_id == product.id).\
            filter(Invoice.invoice_type == 'مشتريات').\
            scalar() or 0
            
        stock_out = db.session.query(func.sum(InvoiceItem.quantity)).\
            join(Invoice).\
            filter(InvoiceItem.product_id == product.id).\
            filter(Invoice.invoice_type == 'مبيعات').\
            scalar() or 0
            
        current_stock = stock_in - stock_out
        
        if current_stock <= low_stock_threshold and current_stock > 0:
            low_stock_products.append({
                'id': product.id,
                'name': product.name,
                'current_stock': current_stock,
                'threshold': low_stock_threshold
            })
    
    return jsonify({
        'success': True,
        'notifications': low_stock_products
    })

# واجهات API للإشعارات
@app.route('/api/notifications')
def api_notifications():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    
    user_id = session['user_id']
    notifications = Notification.query.filter_by(user_id=user_id).order_by(Notification.created_at.desc()).limit(10).all()
    
    result = {
        'notifications': [
            {
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.type,
                'is_read': notification.is_read,
                'time': notification.created_at.strftime('%Y-%m-%d %H:%M')
            }
            for notification in notifications
        ]
    }
    
    return jsonify(result)

@app.route('/api/notifications/count')
def api_notifications_count():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    
    user_id = session['user_id']
    count = Notification.query.filter_by(user_id=user_id, is_read=False).count()
    
    return jsonify({'count': count})

@app.route('/api/notifications/mark_read/<int:notification_id>', methods=['POST'])
def api_mark_notification_read(notification_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    
    user_id = session['user_id']
    notification = Notification.query.filter_by(id=notification_id, user_id=user_id).first()
    
    if not notification:
        return jsonify({'error': 'الإشعار غير موجود'}), 404
    
    notification.is_read = True
    db.session.commit()
    
    return jsonify({'success': True})

@app.route('/api/notifications/mark_all_read', methods=['POST'])
def api_mark_all_notifications_read():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    
    user_id = session['user_id']
    Notification.query.filter_by(user_id=user_id, is_read=False).update({'is_read': True})
    db.session.commit()
    
    return jsonify({'success': True})

# صفحة سجل المهام
@app.route('/task_history')
def task_history():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # هنا يمكن استرجاع سجل المهام من قاعدة البيانات
    # لتبسيط المثال، سنستخدم بيانات ثابتة
    
    tasks = [
        {
            'id': 'task1',
            'name': 'التحقق من المخزون المنخفض',
            'executed_at': '2023-09-15 09:00:00',
            'status': 'success',
            'result': 'تم العثور على 5 منتجات ذات مخزون منخفض'
        },
        {
            'id': 'task2',
            'name': 'النسخ الاحتياطي',
            'executed_at': '2023-09-10 00:00:00',
            'status': 'success',
            'result': 'تم إنشاء النسخة الاحتياطية بنجاح'
        },
        {
            'id': 'task3',
            'name': 'تقرير المبيعات',
            'executed_at': '2023-09-01 08:00:00',
            'status': 'success',
            'result': 'تم إنشاء وإرسال التقرير بنجاح'
        }
    ]
    
    return render_template('task_history.html', tasks=tasks)

# صفحة الإشعارات
@app.route('/notifications')
def notifications():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    user_id = session['user_id']
    notifications = Notification.query.filter_by(user_id=user_id).order_by(Notification.created_at.desc()).all()
    
    return render_template('notifications.html', notifications=notifications)




