from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os
from celery_config import make_celery

app = Flask(__name__, static_folder='static')
app.config['SECRET_KEY'] = 'مفتاح_سري_للتطبيق'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///optical_shop.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعدادات Celery
app.config['CELERY_BROKER_URL'] = 'redis://localhost:6379/0'
app.config['CELERY_RESULT_BACKEND'] = 'redis://localhost:6379/0'

# إنشاء تطبيق Celery
celery = make_celery(app)

db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password = db.Column(db.String(100), nullable=False)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    address = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    category = db.Column(db.String(50))
    price = db.Column(db.Float, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    customer = db.relationship('Customer', backref='invoices')
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'))  # Añadimos la clave foránea
    invoice_type = db.Column(db.String(50), default='مبيعات')
    payment_type = db.Column(db.String(50), default='نقد')
    currency = db.Column(db.String(20), default='ريال يمني')
    status = db.Column(db.String(20), default='مكتملة')
    total_amount = db.Column(db.Float, default=0)
    paid_amount = db.Column(db.Float, default=0)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    items = db.relationship('InvoiceItem', backref='invoice', lazy=True, cascade="all, delete-orphan")
    
    @property
    def remaining_amount(self):
        return self.total_amount - self.paid_amount

class InvoiceItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'))
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'))
    product = db.relationship('Product')
    quantity = db.Column(db.Integer, default=1)
    price = db.Column(db.Float, default=0)
    
    @property
    def total(self):
        return self.price * self.quantity

class Expense(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    expense_type = db.Column(db.String(50), nullable=False)
    amount = db.Column(db.Float, default=0)
    currency = db.Column(db.String(20), default='ريال يمني')
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Supplier(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    address = db.Column(db.String(200))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # La relación con las facturas de compras ahora está correctamente definida
    invoices = db.relationship('Invoice', backref='supplier', lazy=True)

# إضافة نموذج الإشعارات
class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    title = db.Column(db.String(100), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), default='info')  # info, success, warning, danger
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='notifications')

# إضافة نموذج سجل المهام
class TaskLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.String(50), nullable=False)
    task_name = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, success, warning, error
    result = db.Column(db.Text)
    executed_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    user = db.relationship('User', backref='task_logs')

# إضافة معالج الخطأ 404
@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404

# تهيئة قاعدة البيانات وإنشاء مستخدم افتراضي
def initialize_db():
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
        if not User.query.first():
            default_user = User(username='admin', password='admin')
            db.session.add(default_user)
            db.session.commit()

# Recrear la base de datos para asegurar que la estructura coincida con el modelo
def recreate_db():
    with app.app_context():
        db.drop_all()
        db.create_all()
        initialize_db()

# Ejecutar esta función si necesitas recrear la base de datos
# recreate_db()

# استيراد الطرق (routes) بعد تعريف النماذج
import routes

















