<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متابعة الفواتير غير المرحلة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-4">
        <h2 class="text-center mb-4">متابعة الفواتير غير المرحلة</h2>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" id="customerSearch" placeholder="اسم العميل">
                    <button class="btn btn-primary" type="button" id="searchByCustomer">بحث</button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" id="phoneSearch" placeholder="رقم الهاتف">
                    <button class="btn btn-primary" type="button" id="searchByPhone">بحث</button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <input type="date" class="form-control" id="dateSearch">
                    <button class="btn btn-primary" type="button" id="searchByDate">بحث</button>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>المبلغ</th>
                        <th>المدفوع</th>
                        <th>الباقي</th>
                        <th>العملة</th>
                        <th>المستخدم</th>
                        <th>خيارات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in pending_invoices %}
                    <tr>
                        <td>{{ invoice.invoice_number }}</td>
                        <td>{{ invoice.date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ invoice.customer.name }}</td>
                        <td>{{ invoice.total_amount }}</td>
                        <td>{{ invoice.paid_amount }}</td>
                        <td>{{ invoice.remaining }}</td>
                        <td>{{ invoice.currency }}</td>
                        <td>{{ invoice.user.username }}</td>
                        <td>
                            <button class="btn btn-sm btn-primary pay-btn" data-bs-toggle="modal" data-bs-target="#paymentModal" data-invoice-id="{{ invoice.id }}">
                                <i class="bi bi-cash"></i> دفع
                            </button>
                            <button class="btn btn-sm btn-success process-btn" data-invoice-id="{{ invoice.id }}">
                                <i class="bi bi-check-circle"></i> ترحيل
                            </button>
                            <button class="btn btn-sm btn-danger cancel-btn" data-bs-toggle="modal" data-bs-target="#cancelModal" data-invoice-id="{{ invoice.id }}">
                                <i class="bi bi-x-circle"></i> إلغاء
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- نافذة الدفع -->
        <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="paymentModalLabel">تسديد دفعة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="paymentForm" method="post" action="{{ url_for('add_payment') }}">
                            <input type="hidden" name="invoice_id" id="payment_invoice_id">
                            <div class="mb-3">
                                <label class="form-label">المبلغ المتبقي:</label>
                                <input type="number" class="form-control" id="remaining_amount" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المبلغ المدفوع:</label>
                                <input type="number" class="form-control" name="amount" id="payment_amount" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">تاريخ الدفع:</label>
                                <input type="date" class="form-control" name="payment_date" value="{{ today }}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ملاحظات:</label>
                                <textarea class="form-control" name="notes"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" form="paymentForm" class="btn btn-primary">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نافذة الإلغاء -->
        <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="cancelModalLabel">إلغاء الفاتورة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="cancelForm" method="post" action="{{ url_for('cancel_invoice') }}">
                            <input type="hidden" name="invoice_id" id="cancel_invoice_id">
                            <div class="mb-3">
                                <label class="form-label">سبب الإلغاء:</label>
                                <textarea class="form-control" name="cancel_reason" required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">تراجع</button>
                        <button type="submit" form="cancelForm" class="btn btn-danger">تأكيد الإلغاء</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/pending_invoices.js') }}"></script>
</body>
</html>
