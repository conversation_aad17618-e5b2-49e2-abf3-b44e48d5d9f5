<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فواتير المبيعات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-4">
        <h2 class="text-center mb-4">فواتير المبيعات</h2>
        
        <form id="invoiceForm" method="post" action="{{ url_for('create_invoice') }}">
            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">نوع الفاتورة:</label>
                    <select class="form-select" name="invoice_type" required>
                        <option value="مبيعات">مبيعات</option>
                        <option value="شراء">شراء</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">طريقة الدفع:</label>
                    <select class="form-select" id="payment_type" name="payment_type">
                        <option value="نقد">نقد</option>
                        <option value="شيك">شيك</option>
                        <option value="تحويل بنكي">تحويل بنكي</option>
                        <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">العملة:</label>
                    <input type="text" class="form-control" name="currency" value="ريال يمني">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">التاريخ:</label>
                    <input type="date" class="form-control" name="date" value="{{ today }}" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label">رقم الفاتورة:</label>
                    <input type="text" class="form-control" name="invoice_number" value="{{ next_invoice_number }}" readonly>
                </div>
                <div class="col-md-4">
                    <label class="form-label">اسم العميل:</label>
                    <input type="text" class="form-control" name="customer_name" list="customers">
                    <datalist id="customers">
                        {% for customer in customers %}
                        <option value="{{ customer.name }}">{{ customer.phone }}</option>
                        {% endfor %}
                    </datalist>
                </div>
            </div>
            
            <!-- جدول المنتجات -->
            <div class="table-responsive mb-3">
                <table class="table table-bordered" id="productsTable">
                    <thead>
                        <tr>
                            <th>رقم الصنف</th>
                            <th>اسم الصنف</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>الإجمالي</th>
                            <th>حذف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control product-code" name="product_code[]">
                            </td>
                            <td>
                                <input type="text" class="form-control product-name" name="product_name[]">
                            </td>
                            <td>
                                <input type="number" class="form-control product-price" name="product_price[]" min="0">
                            </td>
                            <td>
                                <input type="number" class="form-control product-quantity" name="product_quantity[]" min="1" value="1">
                            </td>
                            <td>
                                <input type="number" class="form-control product-total" name="product_total[]" readonly>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger btn-sm remove-row">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <button type="button" id="addProductRow" class="btn btn-success">
                    <i class="bi bi-plus-circle"></i> إضافة صنف
                </button>
            </div>

            <!-- إجماليات الفاتورة -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">إجمالي الفاتورة:</label>
                    <input type="number" class="form-control" id="totalAmount" name="total_amount" readonly>
                </div>
                <div class="col-md-4">
                    <label class="form-label">المبلغ المدفوع:</label>
                    <input type="number" class="form-control" id="paidAmount" name="paid_amount" min="0">
                </div>
                <div class="col-md-4">
                    <label class="form-label">المتبقي:</label>
                    <input type="number" class="form-control" id="remainingAmount" name="remaining" readonly>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary"><i class="bi bi-save"></i> حفظ الفاتورة</button>
                <button type="button" class="btn btn-info" id="refreshBtn"><i class="bi bi-arrow-clockwise"></i> تحديث</button>
                <button type="button" class="btn btn-success" id="printBtn"><i class="bi bi-printer"></i> طباعة</button>
                <a href="{{ url_for('index') }}" class="btn btn-secondary"><i class="bi bi-arrow-return-right"></i> رجوع</a>
            </div>
        </form>
        
        <hr>
        
        <h4 class="mt-4">فواتير اليوم</h4>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>المبلغ</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>الحالة</th>
                        <th>خيارات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in today_invoices %}
                    <tr>
                        <td>{{ invoice.invoice_number }}</td>
                        <td>{{ invoice.customer.name }}</td>
                        <td>{{ invoice.total_amount }}</td>
                        <td>{{ invoice.paid_amount }}</td>
                        <td>{{ invoice.remaining }}</td>
                        <td>{{ invoice.status }}</td>
                        <td>
                            <a href="{{ url_for('view_invoice', id=invoice.id) }}" class="btn btn-sm btn-info"><i class="bi bi-eye"></i></a>
                            <a href="{{ url_for('print_invoice', id=invoice.id) }}" class="btn btn-sm btn-success"><i class="bi bi-printer"></i></a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/invoice.js') }}"></script>
</body>
</html>



