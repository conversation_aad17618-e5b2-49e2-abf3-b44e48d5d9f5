from app import celery, db, Invoice, InvoiceItem, Product, Customer, Expense, User, Notification
from datetime import datetime, timedelta
from celery import shared_task
import os
import csv
import io
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

@shared_task(ignore_result=False)
def generate_sales_report(start_date, end_date, email=None):
    """
    مهمة لإنشاء تقرير المبيعات وإرساله بالبريد الإلكتروني
    """
    # تحويل التواريخ إلى كائنات datetime
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    end = end.replace(hour=23, minute=59, second=59)
    
    # استعلام الفواتير في الفترة المحددة
    invoices = Invoice.query.filter(
        Invoice.date.between(start, end),
        Invoice.invoice_type == 'مبيعات'
    ).order_by(Invoice.date.desc()).all()
    
    # إنشاء ملف CSV
    output = io.StringIO()
    writer = csv.writer(output)
    
    # كتابة رأس الجدول
    writer.writerow(['رقم الفاتورة', 'التاريخ', 'العميل', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي'])
    
    # كتابة بيانات الفواتير
    for invoice in invoices:
        customer_name = invoice.customer.name if invoice.customer else 'عميل نقدي'
        writer.writerow([
            invoice.invoice_number,
            invoice.date.strftime('%Y-%m-%d %H:%M'),
            customer_name,
            invoice.total_amount,
            invoice.paid_amount,
            invoice.remaining_amount
        ])
    
    # إضافة ملخص
    total_sales = sum(invoice.total_amount for invoice in invoices)
    total_paid = sum(invoice.paid_amount for invoice in invoices)
    total_remaining = sum(invoice.remaining_amount for invoice in invoices)
    
    writer.writerow([])
    writer.writerow(['إجمالي المبيعات', total_sales])
    writer.writerow(['إجمالي المدفوعات', total_paid])
    writer.writerow(['إجمالي المتبقي', total_remaining])
    
    # حفظ الملف
    report_content = output.getvalue()
    output.close()
    
    # إذا تم تحديد بريد إلكتروني، أرسل التقرير
    if email:
        send_email_report(email, f"تقرير المبيعات من {start_date} إلى {end_date}", report_content)
    
    return {
        'status': 'success',
        'message': 'تم إنشاء التقرير بنجاح',
        'report': report_content
    }

@shared_task
def send_email_report(email, subject, report_content):
    """
    مهمة لإرسال التقرير بالبريد الإلكتروني
    """
    try:
        # إعدادات البريد الإلكتروني
        smtp_server = 'smtp.gmail.com'
        smtp_port = 587
        smtp_user = '<EMAIL>'  # استبدل بالبريد الإلكتروني الخاص بك
        smtp_password = 'your_password'  # استبدل بكلمة المرور الخاصة بك
        
        # إنشاء رسالة البريد
        msg = MIMEMultipart()
        msg['From'] = smtp_user
        msg['To'] = email
        msg['Subject'] = subject
        
        # إضافة نص الرسالة
        body = "مرفق تقرير المبيعات المطلوب."
        msg.attach(MIMEText(body, 'plain'))
        
        # إضافة المرفق
        attachment = MIMEApplication(report_content.encode('utf-8'))
        attachment['Content-Disposition'] = f'attachment; filename="sales_report.csv"'
        msg.attach(attachment)
        
        # إرسال البريد
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_user, smtp_password)
        server.send_message(msg)
        server.quit()
        
        return {
            'status': 'success',
            'message': f'تم إرسال التقرير إلى {email} بنجاح'
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': f'حدث خطأ أثناء إرسال البريد الإلكتروني: {str(e)}'
        }

@shared_task
def check_low_stock():
    """
    مهمة للتحقق من المنتجات ذات المخزون المنخفض وإرسال إشعارات
    """
    try:
        # البحث عن المنتجات ذات المخزون المنخفض
        low_stock_products = Product.query.filter(Product.quantity <= Product.min_quantity).all()
        
        if not low_stock_products:
            return {
                'status': 'warning',
                'message': 'لا توجد منتجات ذات مخزون منخفض'
            }
        
        # إنشاء قائمة بالمنتجات ذات المخزون المنخفض
        products_list = []
        for product in low_stock_products:
            products_list.append({
                'id': product.id,
                'name': product.name,
                'code': product.code,
                'quantity': product.quantity,
                'min_quantity': product.min_quantity
            })
        
        # إنشاء إشعار لكل مستخدم
        users = User.query.all()
        for user in users:
            notification = Notification(
                user_id=user.id,
                title='تنبيه: منتجات ذات مخزون منخفض',
                message=f'يوجد {len(low_stock_products)} منتج بحاجة إلى إعادة طلب',
                type='warning'
            )
            db.session.add(notification)
        
        db.session.commit()
        
        return {
            'status': 'success',
            'message': f'تم العثور على {len(low_stock_products)} منتج ذو مخزون منخفض وتم إرسال الإشعارات',
            'products': products_list
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': f'حدث خطأ أثناء التحقق من المخزون المنخفض: {str(e)}'
        }

@shared_task
def backup_database():
    """
    مهمة لعمل نسخة احتياطية من قاعدة البيانات وإرسال إشعار
    """
    try:
        # تحديد مسار قاعدة البيانات
        db_path = 'instance/optical_shop.db'
        
        # إنشاء مجلد للنسخ الاحتياطية إذا لم يكن موجودًا
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # إنشاء اسم ملف النسخة الاحتياطية بالتاريخ والوقت
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"{backup_dir}/optical_shop_{timestamp}.db"
        
        # نسخ ملف قاعدة البيانات
        with open(db_path, 'rb') as src, open(backup_file, 'wb') as dst:
            dst.write(src.read())
        
        # إنشاء إشعار لكل مستخدم
        users = User.query.all()
        for user in users:
            notification = Notification(
                user_id=user.id,
                title='تم عمل نسخة احتياطية',
                message=f'تم عمل نسخة احتياطية بنجاح: {backup_file}',
                type='success'
            )
            db.session.add(notification)
        
        db.session.commit()
        
        return {
            'status': 'success',
            'message': f'تم عمل نسخة احتياطية بنجاح: {backup_file}'
        }
    except Exception as e:
        # إنشاء إشعار خطأ لكل مستخدم
        users = User.query.all()
        for user in users:
            notification = Notification(
                user_id=user.id,
                title='فشل النسخ الاحتياطي',
                message=f'حدث خطأ أثناء عمل النسخة الاحتياطية: {str(e)}',
                type='danger'
            )
            db.session.add(notification)
        
        db.session.commit()
        
        return {
            'status': 'error',
            'message': f'حدث خطأ أثناء عمل النسخة الاحتياطية: {str(e)}'
        }

@shared_task
def generate_monthly_report():
    """
    مهمة لإنشاء تقرير شهري شامل وإرسال إشعارات
    """
    try:
        # تحديد الشهر الحالي
        today = datetime.now()
        first_day = datetime(today.year, today.month, 1)
        if today.month == 12:
            last_day = datetime(today.year + 1, 1, 1) - timedelta(days=1)
        else:
            last_day = datetime(today.year, today.month + 1, 1) - timedelta(days=1)
        
        # استعلام الفواتير في الشهر الحالي
        invoices = Invoice.query.filter(
            Invoice.date.between(first_day, last_day)
        ).all()
        
        # تقسيم الفواتير حسب النوع
        sales_invoices = [inv for inv in invoices if inv.invoice_type == 'مبيعات']
        purchase_invoices = [inv for inv in invoices if inv.invoice_type == 'مشتريات']
        
        # حساب إجماليات المبيعات والمشتريات
        total_sales = sum(inv.total_amount for inv in sales_invoices)
        total_purchases = sum(inv.total_amount for inv in purchase_invoices)
        
        # حساب الأرباح
        profit = 0
        for invoice in sales_invoices:
            for item in invoice.items:
                item_cost = item.product.purchase_price * item.quantity
                item_revenue = item.price * item.quantity
                profit += item_revenue - item_cost
        
        # حساب المصروفات
        expenses = Expense.query.filter(
            Expense.date.between(first_day, last_day)
        ).all()
        total_expenses = sum(expense.amount for expense in expenses)
        
        # حساب صافي الربح
        net_profit = profit - total_expenses
        
        # إنشاء تقرير شامل
        report = {
            'period': f"{first_day.strftime('%Y-%m-%d')} إلى {last_day.strftime('%Y-%m-%d')}",
            'total_sales': total_sales,
            'total_purchases': total_purchases,
            'gross_profit': profit,
            'total_expenses': total_expenses,
            'net_profit': net_profit,
            'sales_count': len(sales_invoices),
            'purchase_count': len(purchase_invoices),
            'expense_count': len(expenses)
        }
        
        # إنشاء إشعار لكل مستخدم
        users = User.query.all()
        for user in users:
            notification = Notification(
                user_id=user.id,
                title='تقرير شهري جديد',
                message=f'تم إنشاء التقرير الشهري لشهر {first_day.strftime("%B %Y")}',
                type='info'
            )
            db.session.add(notification)
        
        db.session.commit()
        
        return {
            'status': 'success',
            'message': 'تم إنشاء التقرير الشهري بنجاح',
            'report': report
        }
    except Exception as e:
        # إنشاء إشعار خطأ لكل مستخدم
        users = User.query.all()
        for user in users:
            notification = Notification(
                user_id=user.id,
                title='فشل إنشاء التقرير الشهري',
                message=f'حدث خطأ أثناء إنشاء التقرير الشهري: {str(e)}',
                type='danger'
            )
            db.session.add(notification)
        
        db.session.commit()
        
        return {
            'status': 'error',
            'message': f'حدث خطأ أثناء إنشاء التقرير الشهري: {str(e)}'
        }

