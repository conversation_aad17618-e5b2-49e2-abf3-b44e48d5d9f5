<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المخزون - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">تقرير المخزون</h1>
            <div>
                <a href="{{ url_for('reports') }}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-left"></i> العودة إلى التقارير
                </a>
                <button class="btn btn-outline-success" id="printReport">
                    <i class="bi bi-printer"></i> طباعة التقرير
                </button>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">إجمالي المنتجات</h3>
                        <h2 class="display-6">{{ products|length }}</h2>
                        <p class="mb-0">منتج</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">قيمة المخزون</h3>
                        <h2 class="display-6">{{ total_inventory_value|round(2) }}</h2>
                        <p class="mb-0">{{ default_currency }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">مخزون منخفض</h3>
                        <h2 class="display-6">{{ low_stock_count }}</h2>
                        <p class="mb-0">منتج</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">نفاد المخزون</h3>
                        <h2 class="display-6">{{ out_of_stock_count }}</h2>
                        <p class="mb-0">منتج</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">توزيع المنتجات حسب الفئة</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="categoriesChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">المنتجات الأعلى قيمة في المخزون</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="topValueChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">تفاصيل المخزون</h5>
                <button class="btn btn-light btn-sm" id="exportCSV">
                    <i class="bi bi-file-earmark-excel"></i> تصدير CSV
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم المنتج</th>
                                <th>الفئة</th>
                                <th>الكمية</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>القيمة الإجمالية</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr class="{% if product.quantity == 0 %}table-danger{% elif product.quantity <= product.min_quantity %}table-warning{% endif %}">
                                <td>{{ product.code }}</td>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category }}</td>
                                <td>{{ product.quantity }}</td>
                                <td>{{ product.purchase_price|round(2) }}</td>
                                <td>{{ product.selling_price|round(2) }}</td>
                                <td>{{ (product.quantity * product.purchase_price)|round(2) }}</td>
                                <td>
                                    {% if product.quantity == 0 %}
                                    <span class="badge bg-danger">نفاد المخزون</span>
                                    {% elif product.quantity <= product.min_quantity %}
                                    <span class="badge bg-warning text-dark">مخزون منخفض</span>
                                    {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني لتوزيع المنتجات حسب الفئة
            const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
            const categoriesChart = new Chart(categoriesCtx, {
                type: 'pie',
                data: {
                    labels: [{% for category, count in categories.items() %}'{{ category }}',{% endfor %}],
                    datasets: [{
                        data: [{% for category, count in categories.items() %}{{ count }},{% endfor %}],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // رسم بياني للمنتجات الأعلى قيمة في المخزون
            const topValueCtx = document.getElementById('topValueChart').getContext('2d');
            const topValueChart = new Chart(topValueCtx, {
                type: 'bar',
                data: {
                    labels: [{% for product in top_value_products %}'{{ product.name }}',{% endfor %}],
                    datasets: [{
                        label: 'قيمة المخزون',
                        data: [{% for product in top_value_products %}{{ product.quantity * product.purchase_price }},{% endfor %}],
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // طباعة التقرير
            document.getElementById('printReport').addEventListener('click', function() {
                window.print();
            });
            
            // تصدير CSV
            document.getElementById('exportCSV').addEventListener('click', function() {
                // إنشاء محتوى CSV
                let csvContent = 'الكود,اسم المنتج,الفئة,الكمية,سعر الشراء,سعر البيع,القيمة الإجمالية,الحالة\n';
                
                {% raw %}
                {% for product in products %}
                let status = '';
                if ({{ product.quantity }} == 0) {
                    status = 'نفاد المخزون';
                } else if ({{ product.quantity }} <= {{ product.min_quantity }}) {
                    status = 'مخزون منخفض';
                } else {
                    status = 'متوفر';
                }
                {% endraw %}
                
                csvContent += '{{ product.code }},"{{ product.name }}",{{ product.category }},{{ product.quantity }},{{ product.purchase_price }},{{ product.selling_price }},{{ product.quantity * product.purchase_price }},'+status+'\n';
                {% endfor %}
                
                // إنشاء رابط تنزيل
                const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent);
                const link = document.createElement('a');
                link.setAttribute('href', encodedUri);
                link.setAttribute('download', 'تقرير_المخزون.csv');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });
        });
    </script>
</body>
</html>
