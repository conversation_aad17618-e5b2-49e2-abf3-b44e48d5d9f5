from app import app, initialize_db, recreate_db, celery
from tasks import check_low_stock, backup_database, generate_monthly_report
from celery.schedules import crontab
import os

# Configuración de tareas periódicas
@celery.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    # Verificar stock bajo cada día a las 9:00 AM
    sender.add_periodic_task(
        crontab(hour=9, minute=0),
        check_low_stock.s(),
        name='check low stock every day at 9:00'
    )
    
    # Backup semanal los domingos a las 12:00 AM
    sender.add_periodic_task(
        crontab(day_of_week=0, hour=0, minute=0),
        backup_database.s(),
        name='backup database every Sunday at 12:00'
    )
    
    # Generar reporte mensual el primer día de cada mes
    sender.add_periodic_task(
        crontab(day_of_month=1, hour=8, minute=0),
        generate_monthly_report.s(),
        name='generate monthly report on first day of month'
    )

if __name__ == '__main__':
    # Forzar la recreación de la base de datos para aplicar los cambios en el modelo
    print("Recreando la base de datos para aplicar los cambios en el modelo...")
    recreate_db()
    
    # Ejecutar la aplicación
    app.run(debug=True, host='0.0.0.0', port=5000)



