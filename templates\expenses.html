<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المصروفات - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-4">
        <div class="row mb-3">
            <div class="col">
                <h2>المصروفات</h2>
            </div>
            <div class="col text-end">
                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-return-right"></i> رجوع
                </a>
                <a href="{{ url_for('expense_report') }}" class="btn btn-info">
                    <i class="bi bi-file-earmark-text"></i> تقرير المصروفات
                </a>
            </div>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">إضافة مصروف جديد</h5>
                    </div>
                    <div class="card-body">
                        <form id="expenseForm" method="post" action="{{ url_for('add_expense') }}">
                            <div class="mb-3">
                                <label class="form-label">التاريخ</label>
                                <input type="date" class="form-control" name="date" value="{{ today }}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">نوع المصروف</label>
                                <input type="text" class="form-control" name="expense_type" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" class="form-control" name="amount" min="0" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">العملة</label>
                                <select class="form-select" name="currency">
                                    <option value="ريال يمني">ريال يمني</option>
                                    <option value="دولار أمريكي">دولار أمريكي</option>
                                    <option value="ريال سعودي">ريال سعودي</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" name="notes" rows="3"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">حفظ المصروف</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">مصروفات اليوم</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>نوع المصروف</th>
                                        <th>المبلغ</th>
                                        <th>العملة</th>
                                        <th>ملاحظات</th>
                                        <th>خيارات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for expense in today_expenses %}
                                    <tr>
                                        <td>{{ expense.date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ expense.expense_type }}</td>
                                        <td>{{ expense.amount }}</td>
                                        <td>{{ expense.currency }}</td>
                                        <td>{{ expense.notes }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-info edit-expense" data-expense-id="{{ expense.id }}">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-expense" data-expense-id="{{ expense.id }}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if not today_expenses %}
                        <div class="text-center py-4">
                            <i class="bi bi-exclamation-circle text-muted" style="font-size: 3rem;"></i>
                            <p class="mt-3 text-muted">لا توجد مصروفات اليوم</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/expenses.js') }}"></script>
</body>
</html>


