document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.2)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // عرض التاريخ والوقت الحالي
    function updateDateTime() {
        const now = new Date();
        const dateOptions = { year: 'numeric', month: 'long', day: 'numeric' };
        const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit' };
        
        const dateStr = now.toLocaleDateString('ar-EG', dateOptions);
        const timeStr = now.toLocaleTimeString('ar-EG', timeOptions);
        
        const dateTimeElement = document.querySelector('.col.text-end span');
        if (dateTimeElement) {
            dateTimeElement.textContent = `التاريخ: ${dateStr} - ${timeStr}`;
        }
    }
    
    // تحديث التاريخ والوقت كل ثانية
    updateDateTime();
    setInterval(updateDateTime, 1000);
});