<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العملاء - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">تقرير العملاء</h1>
            <div>
                <a href="{{ url_for('reports') }}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-arrow-left"></i> العودة إلى التقارير
                </a>
                <button class="btn btn-outline-success" id="printReport">
                    <i class="bi bi-printer"></i> طباعة التقرير
                </button>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">إجمالي العملاء</h3>
                        <h2 class="display-6">{{ customers|length }}</h2>
                        <p class="mb-0">عميل</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">إجمالي المبيعات</h3>
                        <h2 class="display-6">{{ total_all_purchases|round(2) }}</h2>
                        <p class="mb-0">{{ default_currency }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-danger text-white h-100">
                    <div class="card-body text-center">
                        <h3 class="card-title">إجمالي المتبقي</h3>
                        <h2 class="display-6">{{ total_all_remaining|round(2) }}</h2>
                        <p class="mb-0">{{ default_currency }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">أفضل العملاء (حسب المبيعات)</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="topCustomersChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">نسبة المدفوعات والمتبقي</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="paymentsChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">تفاصيل العملاء</h5>
                <button class="btn btn-light btn-sm" id="exportCSV">
                    <i class="bi bi-file-earmark-excel"></i> تصدير CSV
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>عدد الفواتير</th>
                                <th>إجمالي المشتريات</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                            <tr>
                                <td>{{ customer.name }}</td>
                                <td>{{ customer.phone }}</td>
                                <td>{{ customer.email }}</td>
                                <td>{{ customer.invoices_count }}</td>
                                <td>{{ customer.total_purchases|round(2) }}</td>
                                <td>{{ customer.total_paid|round(2) }}</td>
                                <td>{{ customer.total_remaining|round(2) }}</td>
                                <td>
                                    <a href="{{ url_for('customer_details', id=customer.id) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye"></i> التفاصيل
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني لأفضل العملاء
            const topCustomersCtx = document.getElementById('topCustomersChart').getContext('2d');
            const topCustomersChart = new Chart(topCustomersCtx, {
                type: 'bar',
                data: {
                    labels: [{% for customer in top_customers %}'{{ customer.name }}',{% endfor %}],
                    datasets: [{
                        label: 'إجمالي المشتريات',
                        data: [{% for customer in top_customers %}{{ customer.total_purchases }},{% endfor %}],
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // رسم بياني لنسبة المدفوعات والمتبقي
            const paymentsCtx = document.getElementById('paymentsChart').getContext('2d');
            const paymentsChart = new Chart(paymentsCtx, {
                type: 'pie',
                data: {
                    labels: ['المدفوع', 'المتبقي'],
                    datasets: [{
                        data: [{{ total_all_paid }}, {{ total_all_remaining }}],
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(255, 99, 132, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // طباعة التقرير
            document.getElementById('printReport').addEventListener('click', function() {
                window.print();
            });
            
            // تصدير CSV
            document.getElementById('exportCSV').addEventListener('click', function() {
                // إنشاء محتوى CSV
                let csvContent = 'الاسم,رقم الهاتف,البريد الإلكتروني,عدد الفواتير,إجمالي المشتريات,المدفوع,المتبقي\n';
                
                {% for customer in customers %}
                csvContent += '"{{ customer.name }}","{{ customer.phone }}","{{ customer.email }}",{{ customer.invoices_count }},{{ customer.total_purchases }},{{ customer.total_paid }},{{ customer.total_remaining }}\n';
                {% endfor %}
                
                // إنشاء رابط تنزيل
                const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent);
                const link = document.createElement('a');
                link.setAttribute('href', encodedUri);
                link.setAttribute('download', 'تقرير_العملاء.csv');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });
        });
    </script>
</body>
</html>

