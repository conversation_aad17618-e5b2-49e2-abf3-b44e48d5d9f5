<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الفاتورة - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <style>
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 0;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row mb-4 no-print">
            <div class="col-12 text-center">
                <button class="btn btn-primary me-2" onclick="window.print()">
                    <i class="bi bi-printer"></i> طباعة
                </button>
                <a href="{{ url_for('invoices') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-return-right"></i> رجوع
                </a>
            </div>
        </div>
        
        <!-- محتوى الفاتورة للطباعة -->
        <div class="card">
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h2>محل النظارات</h2>
                        <p>العنوان: صنعاء - شارع الزبيري</p>
                        <p>الهاتف: 777123456</p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <p><strong>رقم الفاتورة:</strong> {{ invoice.invoice_number }}</p>
                        <p><strong>التاريخ:</strong> {{ invoice.date.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div class="col-6 text-start">
                        <p><strong>العميل:</strong> {{ invoice.customer.name if invoice.customer else 'عميل نقدي' }}</p>
                        <p><strong>الهاتف:</strong> {{ invoice.customer.phone if invoice.customer and invoice.customer.phone else '-' }}</p>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الكود</th>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in invoice_items %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ item.product.code }}</td>
                                <td>{{ item.product.name }}</td>
                                <td>{{ item.price }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>{{ item.price * item.quantity }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="5" class="text-start"><strong>الإجمالي:</strong></td>
                                <td>{{ invoice.total_amount }}</td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-start"><strong>المدفوع:</strong></td>
                                <td>{{ invoice.paid_amount }}</td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-start"><strong>المتبقي:</strong></td>
                                <td>{{ invoice.remaining }}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <p>شكراً لزيارتكم - نتمنى لكم عودة سعيدة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

