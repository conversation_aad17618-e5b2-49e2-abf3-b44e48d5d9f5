document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('productSelect');
    const copiesInput = document.getElementById('copiesInput');
    const generateBtn = document.getElementById('generateBtn');
    const barcodeContainer = document.getElementById('barcodeContainer');
    const printBtn = document.getElementById('printBtn');
    
    generateBtn.addEventListener('click', function() {
        const selectedOption = productSelect.options[productSelect.selectedIndex];
        if (!selectedOption.value) return;
        
        const productCode = selectedOption.getAttribute('data-code');
        const productName = selectedOption.getAttribute('data-name');
        const productPrice = selectedOption.getAttribute('data-price');
        const copies = parseInt(copiesInput.value) || 1;
        
        barcodeContainer.innerHTML = '';
        
        for (let i = 0; i < copies; i++) {
            const barcodeCard = document.createElement('div');
            barcodeCard.className = 'col-md-4 mb-3';
            barcodeCard.innerHTML = `
                <div class="card">
                    <div class="card-body text-center">
                        <h6>${productName}</h6>
                        <svg class="barcode"></svg>
                        <div class="mt-2">السعر: ${productPrice}</div>
                    </div>
                </div>
            `;
            barcodeContainer.appendChild(barcodeCard);
            
            JsBarcode(barcodeCard.querySelector('.barcode'), productCode, {
                format: "CODE128",
                lineColor: "#000",
                width: 2,
                height: 50,
                displayValue: true
            });
        }
        
        printBtn.classList.remove('d-none');
    });
    
    printBtn.addEventListener('click', function() {
        window.print();
    });
});