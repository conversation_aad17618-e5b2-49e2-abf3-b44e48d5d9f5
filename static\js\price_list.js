document.addEventListener('DOMContentLoaded', function() {
    // تعديل المنتج
    document.querySelectorAll('.edit-product').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            
            // جلب بيانات المنتج من الخادم
            fetch(`/api/product_details/${productId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحويل النموذج إلى نموذج تعديل
                        document.querySelector('#productForm').action = `/edit_product/${productId}`;
                        document.querySelector('#addProductModalLabel').textContent = 'تعديل المنتج';
                        
                        // ملء النموذج ببيانات المنتج
                        document.querySelector('input[name="code"]').value = data.product.code;
                        document.querySelector('input[name="name"]').value = data.product.name;
                        document.querySelector('input[name="category"]').value = data.product.category || '';
                        document.querySelector('input[name="price"]').value = data.product.price;
                        
                        // فتح النافذة المنبثقة
                        new bootstrap.Modal(document.getElementById('addProductModal')).show();
                    }
                })
                .catch(error => console.error('Error:', error));
        });
    });
    
    // حذف المنتج
    document.querySelectorAll('.delete-product').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                window.location.href = `/delete_product/${productId}`;
            }
        });
    });
    
    // طباعة قائمة الأسعار
    document.getElementById('printPriceList').addEventListener('click', function() {
        window.print();
    });
    
    // البحث في قائمة المنتجات
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            const code = row.cells[0].textContent.toLowerCase();
            const name = row.cells[1].textContent.toLowerCase();
            const category = row.cells[2].textContent.toLowerCase();
            
            if (code.includes(searchTerm) || name.includes(searchTerm) || category.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    // إعادة تعيين النموذج عند فتح نافذة إضافة منتج جديد
    document.getElementById('addProductModal').addEventListener('hidden.bs.modal', function() {
        document.querySelector('#productForm').action = '/add_product';
        document.querySelector('#addProductModalLabel').textContent = 'إضافة منتج جديد';
        document.querySelector('#productForm').reset();
    });
});


