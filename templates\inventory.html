<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - نظام محل النظارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">إدارة المخزون</h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> الرئيسية
            </a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">المخزون الحالي</h5>
                <div>
                    <button type="button" class="btn btn-light btn-sm" id="printInventory">
                        <i class="bi bi-printer"></i> طباعة
                    </button>
                    <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#filterModal">
                        <i class="bi bi-funnel"></i> تصفية
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="searchInput" placeholder="بحث عن منتج...">
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رمز المنتج</th>
                                <th>اسم المنتج</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الكمية المتوفرة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>{{ product.code }}</td>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category }}</td>
                                <td>{{ product.price }}</td>
                                <td>
                                    <span class="badge {% if product.current_stock <= 0 %}bg-danger{% elif product.current_stock < 5 %}bg-warning{% else %}bg-success{% endif %}">
                                        {{ product.current_stock }}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-success add-stock" data-product-id="{{ product.id }}" data-product-name="{{ product.name }}">
                                        <i class="bi bi-plus-circle"></i> إضافة مخزون
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نافذة إضافة مخزون -->
    <div class="modal fade" id="addStockModal" tabindex="-1" aria-labelledby="addStockModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addStockModalLabel">إضافة مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ url_for('add_stock') }}" method="post">
                    <div class="modal-body">
                        <input type="hidden" name="product_id" id="stockProductId">
                        <div class="mb-3">
                            <label class="form-label">اسم المنتج:</label>
                            <input type="text" class="form-control" id="stockProductName" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية:</label>
                            <input type="number" class="form-control" name="quantity" min="1" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- نافذة التصفية -->
    <div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterModalLabel">تصفية المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الفئة:</label>
                        <select class="form-select" id="categoryFilter">
                            <option value="">الكل</option>
                            {% set categories = [] %}
                            {% for product in products %}
                                {% if product.category and product.category not in categories %}
                                    {% set _ = categories.append(product.category) %}
                                    <option value="{{ product.category }}">{{ product.category }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">حالة المخزون:</label>
                        <select class="form-select" id="stockStatusFilter">
                            <option value="">الكل</option>
                            <option value="out">نفذ من المخزون</option>
                            <option value="low">مخزون منخفض</option>
                            <option value="available">متوفر</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="applyFilter">تطبيق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة مخزون
            document.querySelectorAll('.add-stock').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');
                    const productName = this.getAttribute('data-product-name');
                    
                    document.getElementById('stockProductId').value = productId;
                    document.getElementById('stockProductName').value = productName;
                    
                    new bootstrap.Modal(document.getElementById('addStockModal')).show();
                });
            });
            
            // البحث في المخزون
            document.getElementById('searchInput').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const tableRows = document.querySelectorAll('tbody tr');
                
                tableRows.forEach(row => {
                    const code = row.cells[0].textContent.toLowerCase();
                    const name = row.cells[1].textContent.toLowerCase();
                    const category = row.cells[2].textContent.toLowerCase();
                    
                    if (code.includes(searchTerm) || name.includes(searchTerm) || category.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
            
            // تصفية المخزون
            document.getElementById('applyFilter').addEventListener('click', function() {
                const categoryFilter = document.getElementById('categoryFilter').value.toLowerCase();
                const stockStatusFilter = document.getElementById('stockStatusFilter').value;
                const tableRows = document.querySelectorAll('tbody tr');
                
                tableRows.forEach(row => {
                    const category = row.cells[2].textContent.toLowerCase();
                    const stockBadge = row.cells[4].querySelector('.badge');
                    const stockValue = parseInt(stockBadge.textContent.trim());
                    
                    let showByCategory = true;
                    let showByStock = true;
                    
                    if (categoryFilter && category !== categoryFilter) {
                        showByCategory = false;
                    }
                    
                    if (stockStatusFilter) {
                        if (stockStatusFilter === 'out' && stockValue > 0) {
                            showByStock = false;
                        } else if (stockStatusFilter === 'low' && (stockValue <= 0 || stockValue >= 5)) {
                            showByStock = false;
                        } else if (stockStatusFilter === 'available' && stockValue < 5) {
                            showByStock = false;
                        }
                    }
                    
                    if (showByCategory && showByStock) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
                
                bootstrap.Modal.getInstance(document.getElementById('filterModal')).hide();
            });
            
            // طباعة المخزون
            document.getElementById('printInventory').addEventListener('click', function() {
                window.print();
            });
        });
    </script>
</body>
</html>