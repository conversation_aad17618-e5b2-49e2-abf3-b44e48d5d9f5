document.addEventListener('DOMContentLoaded', function() {
    // تعديل المصروف
    document.querySelectorAll('.edit-expense').forEach(button => {
        button.addEventListener('click', function() {
            const expenseId = this.getAttribute('data-expense-id');
            
            // جلب بيانات المصروف من الخادم
            fetch(`/api/expense/${expenseId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحويل النموذج إلى نموذج تعديل
                        document.querySelector('#expenseForm').action = '/update_expense';
                        
                        // إضافة حقل معرف المصروف
                        let idInput = document.querySelector('input[name="expense_id"]');
                        if (!idInput) {
                            idInput = document.createElement('input');
                            idInput.type = 'hidden';
                            idInput.name = 'expense_id';
                            document.querySelector('#expenseForm').appendChild(idInput);
                        }
                        idInput.value = data.expense.id;
                        
                        // ملء النموذج بالبيانات
                        document.querySelector('input[name="date"]').value = data.expense.date;
                        
                        const typeSelect = document.querySelector('select[name="expense_type"]');
                        for (let i = 0; i < typeSelect.options.length; i++) {
                            if (typeSelect.options[i].value === data.expense.expense_type) {
                                typeSelect.selectedIndex = i;
                                break;
                            }
                        }
                        
                        document.querySelector('input[name="amount"]').value = data.expense.amount;
                        document.querySelector('input[name="currency"]').value = data.expense.currency;
                        document.querySelector('textarea[name="notes"]').value = data.expense.notes;
                        
                        // تغيير نص زر الحفظ
                        document.querySelector('#expenseForm button[type="submit"]').textContent = 'تحديث المصروف';
                        
                        // إضافة زر إلغاء
                        let cancelButton = document.querySelector('#cancelEdit');
                        if (!cancelButton) {
                            cancelButton = document.createElement('button');
                            cancelButton.id = 'cancelEdit';
                            cancelButton.type = 'button';
                            cancelButton.className = 'btn btn-secondary mt-2 w-100';
                            cancelButton.textContent = 'إلغاء التعديل';
                            document.querySelector('#expenseForm button[type="submit"]').parentNode.appendChild(cancelButton);
                            
                            cancelButton.addEventListener('click', function() {
                                resetForm();
                            });
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
        });
    });
    
    // حذف المصروف
    document.querySelectorAll('.delete-expense').forEach(button => {
        button.addEventListener('click', function() {
            const expenseId = this.getAttribute('data-expense-id');
            
            if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
                window.location.href = `/delete_expense/${expenseId}`;
            }
        });
    });
    
    // إعادة تعيين النموذج
    function resetForm() {
        document.querySelector('#expenseForm').action = '/add_expense';
        document.querySelector('#expenseForm').reset();
        document.querySelector('#expenseForm button[type="submit"]').textContent = 'حفظ المصروف';
        
        const cancelButton = document.querySelector('#cancelEdit');
        if (cancelButton) {
            cancelButton.remove();
        }
        
        const idInput = document.querySelector('input[name="expense_id"]');
        if (idInput) {
            idInput.remove();
        }
    }
});
