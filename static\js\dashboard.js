document.addEventListener('DOMContentLoaded', function() {
    // Función para actualizar la fecha y hora en tiempo real
    function updateDateTime() {
        const now = new Date();
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        };
        
        const dateTimeStr = now.toLocaleDateString('ar-EG', options);
        
        // Actualizar el elemento que muestra la fecha y hora
        const dateElement = document.querySelector('.welcome-subtitle');
        if (dateElement) {
            dateElement.textContent = dateTimeStr;
        }
    }
    
    // Actualizar la fecha y hora cada segundo
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // Animación para las tarjetas de estadísticas
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });
    
    // Agregar efectos de hover a las filas de las tablas
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseover', function() {
            this.style.backgroundColor = 'rgba(13, 110, 253, 0.05)';
        });
        
        row.addEventListener('mouseout', function() {
            this.style.backgroundColor = '';
        });
    });
});